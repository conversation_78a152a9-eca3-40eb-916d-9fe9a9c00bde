﻿using EdTech.Study.Enum;
using EdTech.Study.Exams.Dtos;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions;
using EdTech.Study.Questions.Dtos;
using JetBrains.Annotations;
using Microsoft.Extensions.Logging;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;
using Volo.Abp.Uow;
using static EdTech.Study.Permissions.QuestionsPermissions;
using static System.Collections.Specialized.BitVector32;

namespace EdTech.Study.Exams
{
    public class ExamAppService :
        CrudAppService<
            ExamEntity,
            ExamDto,
            Guid,
            GetExamListDto,
            CreateUpdateExamDto,
            CreateUpdateExamDto>,
        IExamAppService
    {
        private readonly IExamRepository _examRepository;
        private readonly IRepository<SectionEntity, Guid> _sectionRepository;
        private readonly IRepository<QuestionEntity, Guid> _questionRepository;
        private readonly IRepository<QuestionOptionEntity, Guid> _questionOptionRepository;
        private readonly IRepository<GroupQuestions.GroupQuestionEntity, Guid> _groupQuestionRepository;
        private readonly IRepository<SectionEntity, Guid> _examSectionRepository;
        private readonly IRepository<SectionQuestionEntity> _examSectionQuestionRepository;
        private readonly IRepository<SectionGroupQuestionEntity> _examSectionGroupQuestionRepository;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly ILogger<ExamAppService> _logger;
        public ExamAppService(
            IExamRepository examRepository,
            IRepository<SectionEntity, Guid> sectionRepository,
            IRepository<QuestionEntity, Guid> questionRepository,
            IRepository<SectionEntity, Guid> examSectionRepository,
            IRepository<SectionQuestionEntity> examSectionQuestionRepository,
            IUnitOfWorkManager unitOfWorkManager,
            ILogger<ExamAppService> logger,
            IRepository<QuestionOptionEntity, Guid> questionOptionRepository,
            IRepository<GroupQuestions.GroupQuestionEntity, Guid> groupQuestionRepository,
            IRepository<SectionGroupQuestionEntity> examSectionGroupQuestionRepository)
            : base(examRepository)
        {
            _examRepository = examRepository;
            _sectionRepository = sectionRepository;
            _questionRepository = questionRepository;
            _examSectionRepository = examSectionRepository;
            _examSectionQuestionRepository = examSectionQuestionRepository;
            _unitOfWorkManager = unitOfWorkManager;
            _logger = logger;
            _questionOptionRepository = questionOptionRepository;
            _groupQuestionRepository = groupQuestionRepository;
            _examSectionGroupQuestionRepository = examSectionGroupQuestionRepository;
        }

        public override async Task<ExamDto> CreateAsync(CreateUpdateExamDto input)
        {
            await CheckCreatePolicyAsync();
            return await CreateOrUpdateAsync(input);
        }

        public override async Task<ExamDto> UpdateAsync(Guid id, CreateUpdateExamDto input)
        {
            await CheckUpdatePolicyAsync();
            input.Id = id;
            return await CreateOrUpdateAsync(input);
        }

        public override async Task<ExamDto> GetAsync(Guid id)
        {
            var exam = await _examRepository.GetAsync(id);
            var examDto = ObjectMapper.Map<ExamEntity, ExamDto>(exam);
            var sections = (await _sectionRepository
                .WithDetailsAsync(x => x.Questions, x => x.GroupQuestions))
                .Where(x => x.ExamId == id).ToList();
            var sectionDtos = ObjectMapper.Map<List<SectionEntity>, List<ExamSectionDto>>(sections);
            var sectionQuestions = sections.SelectMany(x => x.Questions ?? Enumerable.Empty<SectionQuestionEntity>()).ToList();
            var sectionGroupQuestions = sections.SelectMany(x => x.GroupQuestions ?? Enumerable.Empty<SectionGroupQuestionEntity>()).ToList();
            var questionIds = sectionQuestions.Select(x => x.QuestionId);
            var groupQuestionIds = sectionGroupQuestions.Select(x => x.GroupQuestionId);
            var questions = (await _questionRepository.WithDetailsAsync(
                x => x.Options,
                x => x.FillInBlankAnswers,
                x => x.MatchingAnswers,
                x => x.MatchingItems)).Where(x => questionIds.Contains(x.Id)).ToList();
            var groupQuestions = (await _groupQuestionRepository.WithDetailsAsync(x => x.Questions)).Where(x => groupQuestionIds.Contains(x.Id)).ToList();

            foreach (var sectionDto in sectionDtos)
            {
                sectionDto.Questions = new List<ExamSectionQuestionDto>();
                var currentSectionQuestions = sectionQuestions.Where(x => x.SectionId == sectionDto.Id);
                // Lấy danh sách câu hỏi theo sectionId và không nằm trong group
                if (currentSectionQuestions.Any())
                {
                    foreach (var currentSectionQuestion in currentSectionQuestions)
                    {
                        var questionDto = new ExamSectionQuestionDto()
                        {
                            Id = currentSectionQuestion.Id,
                            ClientId = currentSectionQuestion.Id,
                            QuestionId = currentSectionQuestion.QuestionId,
                            SectionId = sectionDto.Id,
                            SyncQuestion = currentSectionQuestion.SyncQuestion,
                            LastSyncQuestionId = currentSectionQuestion.LastSyncQuestionId,
                            Order = currentSectionQuestion.Order,
                            Score = currentSectionQuestion.Score,
                            SortOrders = currentSectionQuestion.SortOrders
                        };

                        var currentQuestion = questions.FirstOrDefault(x => x.Id == currentSectionQuestion.QuestionId);
                        if (currentQuestion != null)
                        {
                            questionDto.Comment = currentQuestion.Comment;
                            // questionDto.ShuffleOptions = currentQuestion.ShuffleOptions;
                            questionDto.Status = currentQuestion.Status;
                            questionDto.Content = currentQuestion.Content;
                            questionDto.ContentFormat = currentQuestion.ContentFormat;
                            questionDto.Difficulty = currentQuestion.Difficulty;
                            questionDto.Explanation = currentQuestion.Explanation;
                            questionDto.GradeId = currentQuestion.GradeId;
                            questionDto.SubjectId = currentQuestion.SubjectId;
                            questionDto.QuestionType = currentQuestion.QuestionType;
                            questionDto.SourceType = currentQuestion.SourceType;
                            questionDto.Tags = currentQuestion.Tags;
                            questionDto.Topics = currentQuestion.Topics;
                            questionDto.Title = currentQuestion.Title;
                            questionDto.Explanation = currentQuestion.Explanation;

                            // Option
                            if (!currentQuestion.Options.IsNullOrEmpty())
                            {
                                questionDto.Options = ObjectMapper.Map<IList<QuestionOptionEntity>, IList<QuestionOptionDto>>(currentQuestion.Options);
                            }

                            // FillInBlankAnswers
                            if (!currentQuestion.FillInBlankAnswers.IsNullOrEmpty())
                            {
                                questionDto.FillInBlankAnswers = ObjectMapper.Map<IList<FillInBlankAnswerEntity>, IList<FillInBlankAnswerDto>>(currentQuestion.FillInBlankAnswers);
                            }

                            // MatchingItems
                            if (!currentQuestion.MatchingItems.IsNullOrEmpty())
                            {
                                questionDto.MatchingItems = ObjectMapper.Map<IList<MatchingItemEntity>, IList<MatchingItemDto>>(currentQuestion.MatchingItems);
                            }

                            // MatchingAnswers
                            if (!currentQuestion.MatchingAnswers.IsNullOrEmpty())
                            {
                                questionDto.MatchingAnswers = ObjectMapper.Map<IList<MatchingAnswerEntity>, IList<MatchingAnswerDto>>(currentQuestion.MatchingAnswers);
                            }
                        }

                        sectionDto.Questions.Add(questionDto);
                    }
                }

                // Get Group và câu hỏi trong group
                sectionDto.GroupQuestions = new List<ExamSectionGroupQuestionDto>();
                var currentSectionGroupQuestions = sectionGroupQuestions.Where(x => x.SectionId == sectionDto.Id);
                if (currentSectionGroupQuestions.Any())
                {
                    foreach (var currentSectionGroupQuestion in currentSectionGroupQuestions)
                    {
                        var groupQuestionDto = new ExamSectionGroupQuestionDto()
                        {
                            Id = currentSectionGroupQuestion.Id,
                            ClientId = currentSectionGroupQuestion.Id,
                            SectionId = sectionDto.Id,
                            GroupQuestionId = currentSectionGroupQuestion.GroupQuestionId,
                            Order = currentSectionGroupQuestion.Order,
                        };

                        var currentGroupQuestion = groupQuestions.FirstOrDefault(x => x.Id == currentSectionGroupQuestion.GroupQuestionId);
                        if (currentGroupQuestion != null)
                        {
                            groupQuestionDto.Content = currentGroupQuestion.Content;
                            groupQuestionDto.ContentFormat = currentGroupQuestion.ContentFormat;
                            groupQuestionDto.Instructions = currentGroupQuestion.Instructions;

                            var questionGroupIds = currentGroupQuestion.Questions?.Select(x => x.Id).ToList() ?? Enumerable.Empty<Guid>();

                            var questionsInGroup = (await _questionRepository.WithDetailsAsync(x => x.Options, x => x.FillInBlankAnswers, x => x.MatchingAnswers, x => x.MatchingItems)).Where(x => questionGroupIds.Contains(x.Id)).ToList();

                            if (questionsInGroup != null)
                            {
                                foreach (var currentSectionQuestion in questionsInGroup)
                                {
                                    var questionDto = new ExamSectionQuestionDto()
                                    {
                                        Id = currentSectionQuestion.Id,
                                        ClientId = currentSectionQuestion.Id,
                                        QuestionId = currentSectionQuestion.Id,
                                        SectionId = sectionDto.Id,
                                        SyncQuestion = true,
                                    };

                                    questionDto.Comment = currentSectionQuestion.Comment;
                                    // questionDto.ShuffleOptions = currentSectionQuestion.ShuffleOptions;
                                    questionDto.Status = currentSectionQuestion.Status;
                                    questionDto.Content = currentSectionQuestion.Content;
                                    questionDto.ContentFormat = currentSectionQuestion.ContentFormat;
                                    questionDto.Difficulty = currentSectionQuestion.Difficulty;
                                    questionDto.Explanation = currentSectionQuestion.Explanation;
                                    questionDto.GradeId = currentSectionQuestion.GradeId;
                                    questionDto.SubjectId = currentSectionQuestion.SubjectId;
                                    questionDto.QuestionType = currentSectionQuestion.QuestionType;
                                    questionDto.SourceType = currentSectionQuestion.SourceType;
                                    questionDto.Tags = currentSectionQuestion.Tags;
                                    questionDto.Topics = currentSectionQuestion.Topics;
                                    questionDto.Title = currentSectionQuestion.Title;

                                    // Option
                                    if (!currentSectionQuestion.Options.IsNullOrEmpty())
                                    {
                                        questionDto.Options = ObjectMapper.Map<IList<QuestionOptionEntity>, IList<QuestionOptionDto>>(currentSectionQuestion.Options!);
                                    }

                                    // FillInBlankAnswers
                                    if (!currentSectionQuestion.FillInBlankAnswers.IsNullOrEmpty())
                                    {
                                        questionDto.FillInBlankAnswers = ObjectMapper.Map<IList<FillInBlankAnswerEntity>, IList<FillInBlankAnswerDto>>(currentSectionQuestion.FillInBlankAnswers!);
                                    }

                                    // MatchingItems
                                    if (!currentSectionQuestion.MatchingItems.IsNullOrEmpty())
                                    {
                                        questionDto.MatchingItems = ObjectMapper.Map<IList<MatchingItemEntity>, IList<MatchingItemDto>>(currentSectionQuestion.MatchingItems!);
                                    }

                                    // MatchingAnswers
                                    if (!currentSectionQuestion.MatchingAnswers.IsNullOrEmpty())
                                    {
                                        questionDto.MatchingAnswers = ObjectMapper.Map<IList<MatchingAnswerEntity>, IList<MatchingAnswerDto>>(currentSectionQuestion.MatchingAnswers!);
                                    }

                                    groupQuestionDto.Questions.Add(questionDto);
                                }
                            }
                        }
                        sectionDto.GroupQuestions.Add(groupQuestionDto);
                    }
                }
            }
            examDto.Sections = sectionDtos;
            return examDto;
        }

        public override async Task DeleteAsync(Guid id)
        {
            await CheckDeletePolicyAsync();

            var exam = await _examRepository.GetAsync(id);

            if (exam == null)
            {
                throw new UserFriendlyException("Exam not found");
            }

            var sections = await _sectionRepository.GetListAsync(x => x.ExamId == id);
            await _sectionRepository.DeleteManyAsync(sections);
            await base.DeleteAsync(id);
        }

        public async Task<PagedResultDto<ExamDto>> GetListExamAsync(GetExamListDto input)
        {
            var totalCount = await _examRepository.GetCountAsync(
                input.Filter,
                input.ExamType,
                input.StartDate,
                input.EndDate,
                input.Status,
                input.SubjectId,
                input.GradeId);

            var exams = await _examRepository.GetListExamAsync(
                input.Filter,
                input.ExamType,
                input.StartDate,
                input.EndDate,
                input.Status,
                input.SubjectId,
                input.GradeId,
                input.MaxResultCount,
                input.SkipCount,
                input.Sorting);

            return new PagedResultDto<ExamDto>(
                totalCount,
                ObjectMapper.Map<List<ExamEntity>, List<ExamDto>>(exams)
            );
        }

        public async Task<ExamDto> CreateOrUpdateAsync(CreateUpdateExamDto input)
        {
            if (input == null)
            {
                throw new UserFriendlyException("Input cannot be null");
            }

            if (input.Id.HasValue)
            {
                return await UpdateExamAsync(input);
            }

            // Create new exam entity
            var exam = new ExamEntity(Guid.NewGuid())
            {
                Title = input.Title,
                Duration = input.Duration,
                ExamCode = input.ExamCode,
                ExamType = input.ExamType,
                ExamDate = input.ExamDate,
                SourceType = input.SourceType,
                IdempotentKey = string.IsNullOrEmpty(input.IdempotentKey) ? input.CreateIdempotentKey() : input.IdempotentKey,
                Source = input.Title,
                Status = EdTech.Study.Enum.ExamStatus.Draft,
                TotalScore = input.TotalScore,
                ExamPeriod = input.ExamPeriod,
                SubjectId = input.SubjectId,
                GradeId = input.GradeId,
                Description = input.Description,
            };

            // Save exam to get ID
            exam = await _examRepository.InsertAsync(exam, true);

            if (!input.Sections.IsNullOrEmpty())
            {
                await InsertExamSectionAsync(exam, input.Sections);
            }

            return ObjectMapper.Map<ExamEntity, ExamDto>(exam);
        }

        private async Task<ExamDto> UpdateExamAsync(CreateUpdateExamDto input)
        {
            if (!input.Id.HasValue)
            {
                throw new UserFriendlyException("Input ID cannot be null");
            }

            var exam = await _examRepository.GetAsync(input.Id.Value);
            if (exam == null)
            {
                throw new UserFriendlyException("Exam not found");
            }

            await UpdateExamInfoAsync(exam, input);

            var sectionIds = (await _examSectionRepository.GetQueryableAsync())
                   .Where(x => x.ExamId.Equals(exam.Id)).Select(s => s.Id);

            var questionIds = (await _examSectionQuestionRepository.GetQueryableAsync())
                .Where(x => x.SectionId.Equals(sectionIds)).Select(q => q.QuestionId);

            var groupQuestionIds = (await _examSectionGroupQuestionRepository.GetQueryableAsync())
                .Where(x => x.SectionId.Equals(sectionIds)).Select(q => q.GroupQuestionId);

            if (input.Sections.IsNullOrEmpty())
            {
                // Remove Sections
                // TODO: Remove questions need check if they are used in other exams
                // await _questionRepository.DeleteAsync(x => questionIds.Contains(x.Id));
                await _examSectionQuestionRepository.DeleteAsync(x => x.SectionId.Equals(sectionIds));
                await _examSectionRepository.DeleteAsync(x => x.ExamId.Equals(exam.Id));
                await _examSectionGroupQuestionRepository.DeleteAsync(x => x.SectionId.Equals(sectionIds));
            }
            else
            {
                // Update Sections
                var deletedSections = new List<SectionEntity>();
                var updateSectionDtos = new List<CreateUpdateExamSectionDto>();
                var insertSectionDtos = input.Sections.Where(x => !x.Id.HasValue);

                var updatedSections = new List<SectionEntity>();
                // Get existing sections
                var sectionExits = await _examSectionRepository.GetListAsync(x => x.ExamId.Equals(exam.Id), includeDetails: true);
                foreach (var sectionExit in sectionExits)
                {
                    var sectionDto = input.Sections.Where(x => x.Id.HasValue && sectionExit.Id == x.Id).FirstOrDefault();
                    if (sectionDto == null)
                    {
                        deletedSections.Add(sectionExit);
                        continue;
                    }
                    updateSectionDtos.Add(sectionDto);
                }

                // Delete section
                if (deletedSections.Any())
                    await DeletedExamSectionAsync(exam, deletedSections);

                // Update section
                if (updateSectionDtos.Any())
                    await UpdateExamSectionAsync(exam, sectionExits, updateSectionDtos);

                // Insert section
                if (insertSectionDtos.Any())
                    await InsertExamSectionAsync(exam, insertSectionDtos);
            }
            var updatedExam = await _examRepository.FirstAsync(exam => exam.Id == exam.Id);
            return ObjectMapper.Map<ExamEntity, ExamDto>(updatedExam);
        }

        private async Task InsertExamSectionAsync(ExamEntity exam, IEnumerable<CreateUpdateExamSectionDto> insertSectionDtos)
        {
            int sectionIndex = 1;
            foreach (var sectionDto in insertSectionDtos)
            {
                var sectionIdempotentKey = sectionDto.CreateIdempotentKey(exam.IdempotentKey);

                // Create section
                var section = new SectionEntity(Guid.NewGuid())
                {
                    ExamId = exam.Id,
                    Title = sectionDto.Title ?? $"Phần {sectionIndex}",
                    Content = sectionDto.Content,
                    ContentFormat = sectionDto.ContentFormat,
                    OrderIndex = sectionIndex,
                    SectionScore = sectionDto.SectionScore,
                    SourceType = exam.SourceType,
                    IdempotentKey = sectionIdempotentKey,
                    Source = exam.Title,
                    Instructions = sectionDto.Instructions
                };

                sectionIndex++;

                section = await _examSectionRepository.InsertAsync(section, true);

                if (!sectionDto.Questions.IsNullOrEmpty())
                {
                    var exist = await _examSectionRepository.AnyAsync(x => x.Id == section.Id);
                    if (exist)
                    {
                        await InsertQuestionsAsync(exam, section, sectionDto.Questions);
                    }
                    else
                    {
                        _logger.LogError("Section not found {SectionId}", section.Id);
                    }
                }

                // Handle group questions
                if (!sectionDto.GroupQuestions.IsNullOrEmpty())
                {
                    var exist = await _examSectionRepository.AnyAsync(x => x.Id == section.Id);
                    if (exist)
                    {
                        await InsertGroupQuestionsAsync(exam, section, sectionDto.GroupQuestions);
                    }
                    else
                    {
                        _logger.LogError("Section not found {SectionId}", section.Id);
                    }
                }
            }
        }

        [UnitOfWork]
        private async Task UpdateExamSectionAsync(ExamEntity exam, List<SectionEntity> sectionExits, List<CreateUpdateExamSectionDto> updateSectionDtos)
        {
            var sectionUpdateds = new List<SectionEntity>();
            foreach (var sectionDto in updateSectionDtos)
            {
                var section = sectionExits.FirstOrDefault(x => x.Id == sectionDto.Id);
                if (section != null)
                {
                    var i = section.IdempotentKey == section.IdempotentKey;
                    section.Title = sectionDto.Title ?? section.Title;
                    section.Content = sectionDto.Content;
                    section.ContentFormat = sectionDto.ContentFormat;
                    section.OrderIndex = sectionDto.OrderIndex;
                    section.SectionScore = sectionDto.SectionScore;
                    section.Instructions = sectionDto.Instructions;
                    section.Source = exam.Title;
                    sectionUpdateds.Add(section);

                    // Get section questions as lightweight DTOs to avoid tracking
                    var examSectionQuestionDtos = (await _examSectionQuestionRepository
                        .GetQueryableAsync())
                        .Where(x => x.SectionId == section.Id)
                        .Select(sq => new SectionQuestionDto
                        {
                            Id = sq.Id,
                            SectionId = sq.SectionId,
                            QuestionId = sq.QuestionId,
                            Order = sq.Order,
                            Score = sq.Score,
                            SyncQuestion = sq.SyncQuestion,
                            LastSyncQuestionId = sq.LastSyncQuestionId
                        })
                        .ToList();

                    var insertedQuestions = new List<CreateUpdateExamQuestionDto>();
                    var updatedQuestions = new List<CreateUpdateExamQuestionDto>();
                    var deletedSectionQuestions = examSectionQuestionDtos
                        .Where(a => !sectionDto.Questions.Any(b => b.QuestionId == a.QuestionId))
                        .ToList();

                    foreach (var item in sectionDto.Questions)
                    {
                        var existQuestion = examSectionQuestionDtos.FirstOrDefault(x => x.QuestionId == item.QuestionId);
                        if (existQuestion != null)
                        {
                            item.Id = existQuestion.Id; // Set the correct ID from the DTO
                            updatedQuestions.Add(item);
                        }
                        else
                        {
                            insertedQuestions.Add(item);
                        }
                    }

                    insertedQuestions = insertedQuestions.DistinctBy(q => q.ClientId).ToList();
                    updatedQuestions = updatedQuestions.DistinctBy(q => q.Id).ToList();

                    // Convert DTOs back to entities only for deletion
                    var deletedSectionQuestionEntities = deletedSectionQuestions
                        .Select(dto => new SectionQuestionEntity(dto.Id)
                        {
                            SectionId = dto.SectionId,
                            QuestionId = dto.QuestionId
                        })
                        .ToList();

                    if (updatedQuestions.Any())
                    {
                        await UpdateQuestionsAsync(exam, section, examSectionQuestionDtos, updatedQuestions);
                    }

                    if (insertedQuestions.Any())
                    {
                        await InsertQuestionsAsync(exam, section, insertedQuestions);
                    }

                    if (deletedSectionQuestionEntities.Any())
                    {
                        await DeletedQuestionsWithSectionQuestionAsync(exam, section, deletedSectionQuestionEntities);
                    }

                    // Group Questions
                    var examSectionGroupQuestionDtos = (await _examSectionGroupQuestionRepository
                        .GetQueryableAsync())
                        .Where(x => x.SectionId == section.Id)
                        .Select(sq => new ExamSectionGroupQuestionDto
                        {
                            Id = sq.Id,
                            SectionId = sq.SectionId,
                            GroupQuestionId = sq.GroupQuestionId
                        })
                        .ToList();

                    // Update Group Questions
                    if (sectionDto.GroupQuestions != null)
                    {
                        var createGroupQuestions = new List<CreateUpdateGroupQuestionDto>();
                        var updateGroupQuestions = new List<CreateUpdateGroupQuestionDto>();
                        var deleteSectionGroupQuestions = examSectionGroupQuestionDtos
                            .Where(a => !sectionDto.GroupQuestions.Any(b => b.GroupQuestionId == a.GroupQuestionId))
                            .ToList();

                        foreach (var item in sectionDto.GroupQuestions)
                        {
                            var existGroupQuestion = examSectionGroupQuestionDtos.FirstOrDefault(x => x.GroupQuestionId == item.GroupQuestionId);
                            if (existGroupQuestion != null)
                            {
                                item.Id = existGroupQuestion.Id; // Set the correct ID from the DTO
                                updateGroupQuestions.Add(item);
                            }
                            else
                            {
                                createGroupQuestions.Add(item);
                            }
                        }

                        // Handle deleted group questions
                        if (deleteSectionGroupQuestions.Any())
                        {
                            var deletedSectionGroupQuestionIds = deleteSectionGroupQuestions.Select(x => x.Id).ToList();
                            await _examSectionGroupQuestionRepository.DeleteAsync(
                                x => deletedSectionGroupQuestionIds.Contains(x.Id));
                        }

                        // Handle updated group questions
                        if (updateGroupQuestions.Any())
                        {
                            await UpdateGroupQuestionsAsync(exam, section, examSectionGroupQuestionDtos, updateGroupQuestions);
                        }

                        // Handle inserted group questions using the new method
                        if (createGroupQuestions.Any())
                        {
                            await InsertGroupQuestionsAsync(exam, section, createGroupQuestions);
                        }
                    }
                    else
                    {
                        // If no group questions provided, delete all existing ones
                        if (examSectionGroupQuestionDtos.Any())
                        {
                            var deletedSectionGroupQuestionIds = examSectionGroupQuestionDtos.Select(x => x.Id).ToList();
                            await _examSectionGroupQuestionRepository.DeleteAsync(
                                x => deletedSectionGroupQuestionIds.Contains(x.Id));
                        }
                    }
                }
            }

            if (sectionUpdateds.Any())
                await _examSectionRepository.UpdateManyAsync(sectionUpdateds);
        }

        private async Task DeletedQuestionsAsync(SectionEntity section, List<Guid> questionIds)
        {
            if (questionIds.IsNullOrEmpty())
                return;

            // Check if questions are used in other exams
            var questionsInUse = await _examSectionQuestionRepository.GetListAsync(
                x => questionIds.Contains(x.Id) &&
                     x.SectionId != section.Id);

            // Get IDs of questions that are not used elsewhere
            var questionsToDelete = questionIds
                .Where(id => !questionsInUse.Any(q => q.QuestionId == id))
                .ToList();

            // Delete questions that are not used in other exams
            if (questionsToDelete.Any())
            {
                await _questionRepository.DeleteAsync(x => questionsToDelete.Contains(x.Id));
            }
        }

        private async Task DeletedQuestionsWithSectionQuestionAsync(ExamEntity exam, SectionEntity section, List<SectionQuestionEntity> deletedSectionQuestions)
        {
            if (deletedSectionQuestions.IsNullOrEmpty())
                return;

            var questionIds = deletedSectionQuestions.Select(x => x.QuestionId).ToList();

            // Check if questions are used in other exams
            var questionsInUse = await _examSectionQuestionRepository.GetListAsync(
                x => questionIds.Contains(x.Id) &&
                     x.SectionId != section.Id);

            // Get IDs of questions that are not used elsewhere
            var questionsToDelete = questionIds
                .Where(id => !questionsInUse.Any(q => q.QuestionId == id))
                .ToList();

            // Delete questions that are not used in other exams
            if (questionsToDelete.Any())
            {
                await _questionRepository.DeleteAsync(x => questionsToDelete.Contains(x.Id));
            }

            var deletedSectionQuestionIds = deletedSectionQuestions.Select(x => x.Id).ToList();
            // Delete the exam section question relationships
            await _examSectionQuestionRepository.DeleteAsync(
                x => deletedSectionQuestionIds.Contains(x.Id));
        }

        private async Task InsertQuestionsAsync(ExamEntity exam, SectionEntity section, List<CreateUpdateExamQuestionDto> insertedQuestions)
        {
            var createdQuestions = new List<(Guid Id, int Order, float? Score)>();

            // STEP 1: Process each question individually
            var insertQuestions = new List<QuestionEntity>();
            foreach (var questionDto in insertedQuestions)
            {
                // CASE 1: Syncing questions from the question bank
                if (questionDto.SyncQuestion && questionDto.QuestionId.HasValue)
                {
                    // Verify the referenced question exists in the database
                    var questionExists = await _questionRepository.AnyAsync(q => q.Id == questionDto.QuestionId.Value);

                    if (!questionExists)
                    {
                        // Skip this question if it doesn't exist
                        continue;
                    }

                    // Add to our list of questions to link
                    createdQuestions.Add((questionDto.QuestionId.Value, questionDto.Order, questionDto.Score));
                    continue;
                }

                // CASE 2: Creating new questions
                try
                {
                    // Generate idempotent key
                    var questionIdempotentKey = questionDto.CreateIdempotentKey(section.IdempotentKey);

                    // Create the question entity
                    var question = new QuestionEntity(Guid.NewGuid())
                    {
                        Title = questionDto.Title ?? string.Empty,
                        SubjectId = questionDto.SubjectId,
                        GradeId = questionDto.GradeId,
                        Content = questionDto.Content ?? string.Empty,
                        ContentFormat = questionDto.ContentFormat,
                        QuestionType = questionDto.QuestionType,
                        Comment = questionDto.Comment,
                        Difficulty = questionDto.Difficulty,
                        SourceType = exam.SourceType,
                        Status = ExamStatus.Draft,
                        IdempotentKey = questionIdempotentKey,
                        Source = exam.Title,
                        Explanation = questionDto.Explanation,
                        // ShuffleOptions = questionDto.ShuffleOptions,
                        Topics = questionDto.Topics,
                        Tags = questionDto.Tags,
                        CorrectAnswer = questionDto.CorrectAnswer
                    };

                    // Handle options
                    if (questionDto.Options != null && questionDto.Options.Count > 0)
                    {
                        question.Options = new List<QuestionOptionEntity>();
                        int optionIndex = 1;

                        foreach (var optionDto in questionDto.Options)
                        {
                            var optionIdempotentKey = optionDto.CreateIdempotentKey(questionIdempotentKey);
                            var option = new QuestionOptionEntity(Guid.NewGuid())
                            {
                                Content = optionDto.Content,
                                ContentFormat = optionDto.ContentFormat,
                                IsCorrect = optionDto.IsCorrect,
                                Order = optionIndex++,
                                IdempotentKey = optionIdempotentKey,
                                Explanation = optionDto.Explanation,
                                Score = optionDto.Score,
                            };

                            question.Options.Add(option);
                        }
                    }

                    // Handle fill-in-blank answers
                    if (questionDto.FillInBlankAnswers != null && questionDto.FillInBlankAnswers.Count > 0)
                    {
                        question.FillInBlankAnswers = new List<FillInBlankAnswerEntity>();

                        foreach (var answerDto in questionDto.FillInBlankAnswers)
                        {
                            var answer = new FillInBlankAnswerEntity(Guid.NewGuid())
                            {
                                BlankIndex = answerDto.BlankIndex,
                                CaseSensitive = answerDto.CaseSensitive,
                                CorrectAnswers = answerDto.CorrectAnswers,
                                Feedback = answerDto.Feedback,
                                Score = answerDto.Score,
                            };

                            question.FillInBlankAnswers.Add(answer);
                        }
                    }

                    // Handle matching items 
                    Dictionary<Guid, Guid> clientIdToEntityIdMap = new Dictionary<Guid, Guid>();
                    if (questionDto.MatchingItems != null && questionDto.MatchingItems.Count > 0)
                    {
                        question.MatchingItems = new List<MatchingItemEntity>();

                        foreach (var itemDto in questionDto.MatchingItems)
                        {
                            var item = new MatchingItemEntity(Guid.NewGuid())
                            {
                                Content = itemDto.Content,
                                Order = itemDto.Order,
                                ContentFormat = itemDto.ContentFormat,
                                Type = itemDto.Type,
                            };

                            question.MatchingItems.Add(item);

                            // Map client-side ID to server-side ID for matching answers
                            if (itemDto.ClientId != Guid.Empty)
                            {
                                clientIdToEntityIdMap[itemDto.ClientId] = item.Id;
                            }
                        }
                    }

                    // Handle matching answers
                    if (questionDto.MatchingAnswers != null && questionDto.MatchingAnswers.Count > 0)
                    {
                        question.MatchingAnswers = new List<MatchingAnswerEntity>();

                        foreach (var answerDto in questionDto.MatchingAnswers)
                        {
                            if (clientIdToEntityIdMap.TryGetValue(answerDto.PremiseId, out var premiseId) &&
                                clientIdToEntityIdMap.TryGetValue(answerDto.ResponseId, out var responseId))
                            {
                                var answer = new MatchingAnswerEntity(Guid.NewGuid())
                                {
                                    PremiseId = premiseId,
                                    ResponseId = responseId,
                                    Feedback = answerDto.Feedback,
                                    Score = answerDto.Score,
                                };

                                question.MatchingAnswers.Add(answer);
                            }
                        }
                    }

                    // Insert the question
                    // var savedQuestion = await _questionRepository.InsertAsync(question, true);
                    insertQuestions.Add(question);
                    // Add to our list of questions to link
                    // createdQuestions.Add((savedQuestion.Id, questionDto.Order, questionDto.Score));
                }
                catch (Exception ex)
                {
                    // Log the error but continue with other questions
                    // Using structured logging if available
                    _logger.LogError(ex, "Failed to create question. Details: {ErrorMessage}", ex.Message);
                }
            }

            await _questionRepository.InsertManyAsync(insertQuestions, true);
            foreach (var question in insertQuestions)
            {
                var currentQuestion = await _questionRepository.GetAsync(question.Id);
                createdQuestions.Add((currentQuestion.Id, 0, 1.0f));
            }

            // STEP 2: Create section-question relationships in a separate transaction
            var sectionQuestionsToInsert = new List<SectionQuestionEntity>();
            foreach (var (questionId, order, score) in createdQuestions)
            {
                // Verify the question still exists before creating the relationship
                var questionExists = await _questionRepository.AnyAsync(q => q.Id == questionId);
                var options = insertQuestions.FirstOrDefault(x => x.Id == questionId)?.Options;
                var sortOrders = options?
                                .Select(x => new ItemOrder(x.Id, x.Order))
                                .OrderBy(x => x.Order)
                                .ToList();
                if (!questionExists)
                {
                    _logger.LogWarning("Question with ID {QuestionId} not found when creating section link", questionId);
                    continue;
                }

                var sectionQuestion = new SectionQuestionEntity(Guid.NewGuid())
                {
                    SectionId = section.Id,
                    QuestionId = questionId,
                    Order = order,
                    Score = score,
                    SyncQuestion = false,
                    SortOrders = sortOrders
                };

                sectionQuestionsToInsert.Add(sectionQuestion);
            }

            await _examSectionQuestionRepository.InsertManyAsync(sectionQuestionsToInsert, true);
        }

        private async Task InsertGroupQuestionsAsync(ExamEntity exam, SectionEntity section, List<CreateUpdateGroupQuestionDto> groupQuestions)
        {
            if (groupQuestions.IsNullOrEmpty())
                return;

            foreach (var groupQuestionDto in groupQuestions)
            {
                try
                {
                    // Create the group question entity
                    var groupQuestion = new GroupQuestionEntity(Guid.NewGuid())
                    {
                        Content = groupQuestionDto.Content ?? string.Empty,
                        ContentFormat = groupQuestionDto.ContentFormat,
                        Instructions = groupQuestionDto.Instructions,
                        Questions = new List<QuestionEntity>()
                    };

                    // Insert the group question first to get its ID
                    groupQuestion = await _groupQuestionRepository.InsertAsync(groupQuestion, true);

                    // Create and insert questions for this group
                    if (!groupQuestionDto.Questions.IsNullOrEmpty())
                    {
                        var questionsToInsert = new List<QuestionEntity>();
                        foreach (var questionDto in groupQuestionDto.Questions)
                        {
                            // Generate idempotent key for the question
                            var questionIdempotentKey = questionDto.CreateIdempotentKey(section.IdempotentKey);

                            // Create the question entity
                            var question = new QuestionEntity(Guid.NewGuid())
                            {
                                Title = questionDto.Title ?? string.Empty,
                                SubjectId = questionDto.SubjectId,
                                GradeId = questionDto.GradeId,
                                Content = questionDto.Content ?? string.Empty,
                                ContentFormat = questionDto.ContentFormat,
                                QuestionType = questionDto.QuestionType,
                                Comment = questionDto.Comment,
                                Difficulty = questionDto.Difficulty,
                                SourceType = exam.SourceType,
                                Status = ExamStatus.Draft,
                                IdempotentKey = questionIdempotentKey,
                                Source = exam.Title,
                                Explanation = questionDto.Explanation,
                                // ShuffleOptions = questionDto.ShuffleOptions,
                                Topics = questionDto.Topics,
                                Tags = questionDto.Tags,
                                GroupQuestionId = groupQuestion.Id
                            };

                            // Handle options
                            if (questionDto.Options != null && questionDto.Options.Count > 0)
                            {
                                question.Options = new List<QuestionOptionEntity>();
                                int optionIndex = 1;

                                foreach (var optionDto in questionDto.Options)
                                {
                                    var optionIdempotentKey = optionDto.CreateIdempotentKey(questionIdempotentKey);
                                    var option = new QuestionOptionEntity(Guid.NewGuid())
                                    {
                                        Content = optionDto.Content,
                                        ContentFormat = optionDto.ContentFormat,
                                        IsCorrect = optionDto.IsCorrect,
                                        Order = optionIndex++,
                                        IdempotentKey = optionIdempotentKey,
                                        Explanation = optionDto.Explanation,
                                        Score = optionDto.Score,
                                    };

                                    question.Options.Add(option);
                                }
                            }

                            // Handle fill-in-blank answers
                            if (questionDto.FillInBlankAnswers != null && questionDto.FillInBlankAnswers.Count > 0)
                            {
                                question.FillInBlankAnswers = new List<FillInBlankAnswerEntity>();

                                foreach (var answerDto in questionDto.FillInBlankAnswers)
                                {
                                    var answer = new FillInBlankAnswerEntity(Guid.NewGuid())
                                    {
                                        BlankIndex = answerDto.BlankIndex,
                                        CaseSensitive = answerDto.CaseSensitive,
                                        CorrectAnswers = answerDto.CorrectAnswers,
                                        Feedback = answerDto.Feedback,
                                        Score = answerDto.Score,
                                    };

                                    question.FillInBlankAnswers.Add(answer);
                                }
                            }

                            // Handle matching items
                            Dictionary<Guid, Guid> clientIdToEntityIdMap = new Dictionary<Guid, Guid>();
                            if (questionDto.MatchingItems != null && questionDto.MatchingItems.Count > 0)
                            {
                                question.MatchingItems = new List<MatchingItemEntity>();

                                foreach (var itemDto in questionDto.MatchingItems)
                                {
                                    var item = new MatchingItemEntity(Guid.NewGuid())
                                    {
                                        Content = itemDto.Content,
                                        Order = itemDto.Order,
                                        ContentFormat = itemDto.ContentFormat,
                                        Type = itemDto.Type,
                                    };

                                    question.MatchingItems.Add(item);

                                    if (itemDto.ClientId != Guid.Empty)
                                    {
                                        clientIdToEntityIdMap[itemDto.ClientId] = item.Id;
                                    }
                                }
                            }

                            // Handle matching answers
                            if (questionDto.MatchingAnswers != null && questionDto.MatchingAnswers.Count > 0)
                            {
                                question.MatchingAnswers = new List<MatchingAnswerEntity>();

                                foreach (var answerDto in questionDto.MatchingAnswers)
                                {
                                    if (clientIdToEntityIdMap.TryGetValue(answerDto.PremiseId, out var premiseId) &&
                                        clientIdToEntityIdMap.TryGetValue(answerDto.ResponseId, out var responseId))
                                    {
                                        var answer = new MatchingAnswerEntity(Guid.NewGuid())
                                        {
                                            PremiseId = premiseId,
                                            ResponseId = responseId,
                                            Feedback = answerDto.Feedback,
                                            Score = answerDto.Score,
                                        };

                                        question.MatchingAnswers.Add(answer);
                                    }
                                }
                            }

                            questionsToInsert.Add(question);
                        }

                        // Insert all questions for this group
                        await _questionRepository.InsertManyAsync(questionsToInsert, true);
                    }

                    // Create the section-group question relationship
                    var sectionGroupQuestion = new SectionGroupQuestionEntity(Guid.NewGuid())
                    {
                        SectionId = section.Id,
                        GroupQuestionId = groupQuestion.Id
                    };

                    await _examSectionGroupQuestionRepository.InsertAsync(sectionGroupQuestion, true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to create group question. Details: {ErrorMessage}", ex.Message);
                    // Continue with other group questions even if one fails
                }
            }
        }

        private async Task UpdateQuestionsAsync(ExamEntity exam, SectionEntity section, List<SectionQuestionDto> examSectionQuestionDtos, List<CreateUpdateExamQuestionDto> updatedQuestions)
        {
            var questionIds = updatedQuestions.Where(x => !x.SyncQuestion).Select(q => q.QuestionId).ToList();

            // Fetch full question entities to get current ConcurrencyStamp
            var questions = await _questionRepository.GetListAsync(q => questionIds.Contains(q.Id));
            var questionDtos = ObjectMapper.Map<List<QuestionEntity>, List<QuestionDto>>(questions);

            var updatedExamSectionQuestions = new List<SectionQuestionEntity>();

            foreach (var questionDto in updatedQuestions)
            {
                if (!questionDto.Id.HasValue)
                    continue;

                var sectionQuestionDto = examSectionQuestionDtos.FirstOrDefault(x => x.Id == questionDto.Id);
                if (sectionQuestionDto == null)
                    continue;

                // Create new entity for update
                var optionSortOrders = questionDto.Options?
                    .Select(x => new ItemOrder(x.Id ?? Guid.Empty, x.Order))
                    .OrderBy(x => x.Order).ToList();

                var sectionQuestion = new SectionQuestionEntity(sectionQuestionDto.Id)
                {
                    SectionId = section.Id,
                    QuestionId = questionDto.QuestionId ?? sectionQuestionDto.QuestionId,
                    Order = questionDto.Order,
                    Score = questionDto.Score,
                    SyncQuestion = questionDto.SyncQuestion,
                    LastSyncQuestionId = questionDto.QuestionId,
                    SortOrders = optionSortOrders
                };

                try
                {
                    // Update section question and catch concurrency exception
                    await _examSectionQuestionRepository.UpdateAsync(sectionQuestion, true);
                    updatedExamSectionQuestions.Add(sectionQuestion);
                }
                catch (AbpDbConcurrencyException ex)
                {
                    // Log the exception
                    _logger.LogWarning("Concurrency exception when updating section question {Id}: {Message}",
                        sectionQuestion.Id, ex.Message);

                    // Skip this item and continue
                    continue;
                }

                if (questionDto.SyncQuestion)
                    continue;

                // Get the existing question with its current state and ConcurrencyStamp
                var existingQuestion = questions.FirstOrDefault(q => q.Id == questionDto.QuestionId);
                if (existingQuestion == null)
                    continue;

                // Update properties on the existing entity to maintain ConcurrencyStamp
                existingQuestion.Title = questionDto.Title ?? existingQuestion.Title;
                existingQuestion.Content = questionDto.Content ?? existingQuestion.Content;
                existingQuestion.ContentFormat = questionDto.ContentFormat;
                existingQuestion.QuestionType = questionDto.QuestionType;
                existingQuestion.Difficulty = questionDto.Difficulty;
                existingQuestion.Explanation = questionDto.Explanation;
                // existingQuestion.ShuffleOptions = questionDto.ShuffleOptions;
                existingQuestion.SubjectId = questionDto.SubjectId;
                existingQuestion.GradeId = questionDto.GradeId;
                existingQuestion.SourceType = exam.SourceType;
                existingQuestion.Source = exam.Title;
                existingQuestion.Topics = questionDto.Topics;
                existingQuestion.Tags = questionDto.Tags;
                existingQuestion.CorrectAnswer = questionDto.CorrectAnswer;

                if (questionDto.Status != null)
                {
                    existingQuestion.Status = questionDto.Status.Value;
                }

                try
                {
                    // Update the question with existing entity that has proper ConcurrencyStamp
                    await _questionRepository.UpdateAsync(existingQuestion, true);
                }
                catch (AbpDbConcurrencyException ex)
                {
                    // Log the exception
                    _logger.LogWarning("Concurrency exception when updating question {Id}: {Message}",
                        existingQuestion.Id, ex.Message);

                    // You could implement a retry mechanism here:
                    // 1. Reload the entity with fresh data
                    // var freshQuestion = await _questionRepository.GetAsync(existingQuestion.Id);
                    // 2. Apply changes to fresh entity
                    // 3. Try update again

                    // For now, just log and continue
                }


                // Update question options
                if (!questionDto.Options.IsNullOrEmpty())
                {
                    // Get existing options from the database
                    var existingOptions = await _questionOptionRepository.GetListAsync(
                        predicate: o => o.QuestionId == existingQuestion.Id
                    );

                    // Split the options into groups
                    var updatedOptions = questionDto.Options.Where(x => x.Id.HasValue).ToList();
                    var insertedOptions = questionDto.Options.Where(x => !x.Id.HasValue).ToList();
                    var deletedOptions = existingOptions
                        .Where(a => !questionDto.Options.Any(b => b.Id == a.Id))
                        .ToList();

                    // Delete options
                    if (deletedOptions.Any())
                    {
                        foreach (var option in deletedOptions)
                        {
                            await _questionOptionRepository.DeleteAsync(option);
                        }
                    }

                    // Update existing options
                    if (updatedOptions.Any())
                    {
                        foreach (var optionDto in updatedOptions)
                        {
                            var option = existingOptions.FirstOrDefault(x => x.Id == optionDto.Id);
                            if (option != null)
                            {
                                option.Content = optionDto.Content;
                                option.ContentFormat = optionDto.ContentFormat;
                                option.IsCorrect = optionDto.IsCorrect;
                                option.Explanation = optionDto.Explanation;
                                option.Score = optionDto.Score;
                                // option.Order = optionDto.Order;

                                try
                                {
                                    await _questionOptionRepository.UpdateAsync(option, true);
                                }
                                catch (AbpDbConcurrencyException ex)
                                {
                                    _logger.LogWarning("Concurrency exception when updating option {Id}: {Message}",
                                        option.Id, ex.Message);
                                }
                            }
                        }
                    }

                    // Insert new options
                    if (insertedOptions.Any())
                    {
                        int optionIndex = existingOptions.Count > 0 ? existingOptions.Max(o => o.Order) + 1 : 1;
                        foreach (var optionDto in insertedOptions)
                        {
                            var optionIdempotentKey = optionDto.CreateIdempotentKey(existingQuestion.IdempotentKey);
                            var option = new QuestionOptionEntity(Guid.NewGuid())
                            {
                                QuestionId = existingQuestion.Id,
                                Content = optionDto.Content,
                                ContentFormat = optionDto.ContentFormat,
                                IsCorrect = optionDto.IsCorrect,
                                Order = optionDto.Order != 0 ? optionDto.Order : optionIndex++,
                                IdempotentKey = optionIdempotentKey,
                                Explanation = optionDto.Explanation,
                                Score = optionDto.Score
                            };

                            await _questionOptionRepository.InsertAsync(option, true);
                        }
                    }
                }
            }
        }

        private async Task UpdateGroupQuestionsAsync(ExamEntity exam, SectionEntity section, List<ExamSectionGroupQuestionDto> examSectionGroupQuestionDtos, List<CreateUpdateGroupQuestionDto> updatedGroupQuestions)
        {
            // Update SectionGroupQuestionEntities
            var sectionGroupQuestionToUpdateIds = examSectionGroupQuestionDtos.Select(g => g.Id).Distinct().ToList();
            var sectionGroupQuestionsToUpdate = await _examSectionGroupQuestionRepository.GetListAsync(x => sectionGroupQuestionToUpdateIds.Contains(x.Id));
            foreach (var sectionGroupQuestion in sectionGroupQuestionsToUpdate)
            {
                var groupQuestionDto = updatedGroupQuestions.FirstOrDefault(x => x.Id == sectionGroupQuestion.Id);
                if (groupQuestionDto == null)
                {
                    _logger.LogWarning("SectionGroupQuestion not found for ID {Id}", sectionGroupQuestion.GroupQuestionId);
                    continue;
                }
                sectionGroupQuestion.Order = groupQuestionDto.Order;
            }
            await _examSectionGroupQuestionRepository.UpdateManyAsync(sectionGroupQuestionsToUpdate);

            // Update GroupEntities
            foreach (var groupQuestionDto in updatedGroupQuestions)
            {
                if (!groupQuestionDto.Id.HasValue)
                {
                    _logger.LogWarning("Group question ID is required for update");
                    continue;
                }

                var sectionGroupQuestionDto = examSectionGroupQuestionDtos.FirstOrDefault(x => x.Id == groupQuestionDto.Id);

                if (sectionGroupQuestionDto == null)
                {
                    _logger.LogWarning("Section group question not found for ID {Id}", groupQuestionDto.Id);
                    continue;
                }

                try
                {
                    // Get the existing group question with its questions
                    var groupEntity = await _groupQuestionRepository.GetAsync(sectionGroupQuestionDto.GroupQuestionId);
                    if (groupEntity == null)
                    {
                        _logger.LogWarning("Group question entity not found for ID {Id}", sectionGroupQuestionDto.GroupQuestionId);
                        continue;
                    }

                    // Update basic group question properties
                    groupEntity.Content = groupQuestionDto.Content ?? groupEntity.Content;
                    groupEntity.ContentFormat = groupQuestionDto.ContentFormat;
                    groupEntity.Instructions = groupQuestionDto.Instructions;

                    // Handle questions in the group
                    if (!groupQuestionDto.Questions.IsNullOrEmpty())
                    {
                        // Get existing questions in the group
                        var existingQuestions = await _questionRepository.GetListAsync(q => q.GroupQuestionId == groupEntity.Id);
                        var existingQuestionIds = existingQuestions.Select(q => q.Id).ToList();

                        // Split questions into categories
                        var questionsToUpdate = groupQuestionDto.Questions.Where(q => q.QuestionId.HasValue && existingQuestionIds.Contains(q.QuestionId.Value)).ToList();
                        var questionsToInsert = groupQuestionDto.Questions.Where(q => !q.QuestionId.HasValue || !existingQuestionIds.Contains(q.QuestionId.Value)).ToList();
                        var questionsToDelete = existingQuestions.Where(q => !groupQuestionDto.Questions.Any(gq => gq.QuestionId == q.Id)).ToList();

                        // Delete questions that are no longer in the group
                        if (questionsToDelete.Any())
                        {
                            await _questionRepository.DeleteManyAsync(questionsToDelete);
                        }

                        // Update existing questions
                        foreach (var questionDto in questionsToUpdate)
                        {
                            var existingQuestion = existingQuestions.FirstOrDefault(q => q.Id == questionDto.QuestionId);
                            if (existingQuestion == null) continue;

                            // Update question properties
                            existingQuestion.Title = questionDto.Title ?? existingQuestion.Title;
                            existingQuestion.Content = questionDto.Content ?? existingQuestion.Content;
                            existingQuestion.ContentFormat = questionDto.ContentFormat;
                            existingQuestion.QuestionType = questionDto.QuestionType;
                            existingQuestion.Difficulty = questionDto.Difficulty;
                            existingQuestion.Explanation = questionDto.Explanation;
                            // existingQuestion.ShuffleOptions = questionDto.ShuffleOptions;
                            existingQuestion.SubjectId = questionDto.SubjectId;
                            existingQuestion.GradeId = questionDto.GradeId;
                            existingQuestion.SourceType = exam.SourceType;
                            existingQuestion.Source = exam.Title;
                            existingQuestion.Topics = questionDto.Topics;
                            existingQuestion.Tags = questionDto.Tags;
                            if (questionDto.Status.HasValue)
                            {
                                existingQuestion.Status = questionDto.Status.Value;
                            }

                            try
                            {
                                await _questionRepository.UpdateAsync(existingQuestion, true);

                                // Update question options if provided
                                if (!questionDto.Options.IsNullOrEmpty())
                                {
                                    var existingOptions = await _questionOptionRepository.GetListAsync(o => o.QuestionId == existingQuestion.Id);

                                    // Split options into categories
                                    var optionsToUpdate = questionDto.Options.Where(o => o.Id.HasValue).ToList();
                                    var optionsToInsert = questionDto.Options.Where(o => !o.Id.HasValue).ToList();
                                    var optionsToDelete = existingOptions.Where(o => !questionDto.Options.Any(no => no.Id == o.Id)).ToList();

                                    // Delete removed options
                                    if (optionsToDelete.Any())
                                    {
                                        await _questionOptionRepository.DeleteManyAsync(optionsToDelete);
                                    }

                                    // Update existing options
                                    foreach (var optionDto in optionsToUpdate)
                                    {
                                        var existingOption = existingOptions.FirstOrDefault(o => o.Id == optionDto.Id);
                                        if (existingOption == null) continue;

                                        existingOption.Content = optionDto.Content;
                                        existingOption.ContentFormat = optionDto.ContentFormat;
                                        existingOption.IsCorrect = optionDto.IsCorrect;
                                        existingOption.Explanation = optionDto.Explanation;
                                        existingOption.Score = optionDto.Score;

                                        try
                                        {
                                            await _questionOptionRepository.UpdateAsync(existingOption, true);
                                        }
                                        catch (AbpDbConcurrencyException ex)
                                        {
                                            _logger.LogWarning("Concurrency exception when updating option {Id}: {Message}",
                                                existingOption.Id, ex.Message);
                                        }
                                    }

                                    // Insert new options
                                    if (optionsToInsert.Any())
                                    {
                                        int optionIndex = existingOptions.Count > 0 ? existingOptions.Max(o => o.Order) + 1 : 1;
                                        foreach (var optionDto in optionsToInsert)
                                        {
                                            var optionIdempotentKey = optionDto.CreateIdempotentKey(existingQuestion.IdempotentKey);
                                            var newOption = new QuestionOptionEntity(Guid.NewGuid())
                                            {
                                                QuestionId = existingQuestion.Id,
                                                Content = optionDto.Content,
                                                ContentFormat = optionDto.ContentFormat,
                                                IsCorrect = optionDto.IsCorrect,
                                                Order = optionDto.Order != 0 ? optionDto.Order : optionIndex++,
                                                IdempotentKey = optionIdempotentKey,
                                                Explanation = optionDto.Explanation,
                                                Score = optionDto.Score
                                            };

                                            await _questionOptionRepository.InsertAsync(newOption, true);
                                        }
                                    }
                                }
                            }
                            catch (AbpDbConcurrencyException ex)
                            {
                                _logger.LogWarning("Concurrency exception when updating question {Id}: {Message}",
                                    existingQuestion.Id, ex.Message);
                            }
                        }

                        // Insert new questions
                        if (questionsToInsert.Any())
                        {
                            var newQuestions = new List<QuestionEntity>();
                            foreach (var questionDto in questionsToInsert)
                            {
                                var questionIdempotentKey = questionDto.CreateIdempotentKey(section.IdempotentKey);
                                var newQuestion = new QuestionEntity(Guid.NewGuid())
                                {
                                    Title = questionDto.Title ?? string.Empty,
                                    SubjectId = questionDto.SubjectId,
                                    GradeId = questionDto.GradeId,
                                    Content = questionDto.Content ?? string.Empty,
                                    ContentFormat = questionDto.ContentFormat,
                                    QuestionType = questionDto.QuestionType,
                                    Comment = questionDto.Comment,
                                    Difficulty = questionDto.Difficulty,
                                    SourceType = exam.SourceType,
                                    Status = ExamStatus.Draft,
                                    IdempotentKey = questionIdempotentKey,
                                    Source = exam.Title,
                                    Explanation = questionDto.Explanation,
                                    // ShuffleOptions = questionDto.ShuffleOptions,
                                    Topics = questionDto.Topics,
                                    Tags = questionDto.Tags,
                                    GroupQuestionId = groupEntity.Id
                                };

                                // Handle options for new questions
                                if (!questionDto.Options.IsNullOrEmpty())
                                {
                                    newQuestion.Options = new List<QuestionOptionEntity>();
                                    int optionIndex = 1;

                                    foreach (var optionDto in questionDto.Options)
                                    {
                                        var optionIdempotentKey = optionDto.CreateIdempotentKey(questionIdempotentKey);
                                        var option = new QuestionOptionEntity(Guid.NewGuid())
                                        {
                                            Content = optionDto.Content,
                                            ContentFormat = optionDto.ContentFormat,
                                            IsCorrect = optionDto.IsCorrect,
                                            Order = optionIndex++,
                                            IdempotentKey = optionIdempotentKey,
                                            Explanation = optionDto.Explanation,
                                            Score = optionDto.Score,
                                        };

                                        newQuestion.Options.Add(option);
                                    }
                                }

                                newQuestions.Add(newQuestion);
                            }

                            await _questionRepository.InsertManyAsync(newQuestions, true);
                        }
                    }

                    // Update the group question entity
                    await _groupQuestionRepository.UpdateAsync(groupEntity, true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to update group question {Id}. Details: {ErrorMessage}",
                        groupQuestionDto.Id, ex.Message);
                }
            }
        }

        private async Task UpdateExamInfoAsync(ExamEntity exam, CreateUpdateExamDto input)
        {
            string? i = exam.IdempotentKey;
            string? s = exam.Source;

            ObjectMapper.Map(input, exam);
            exam.IdempotentKey = i;
            exam.Source = s;

            await _examRepository.UpdateAsync(exam, true);
        }

        private async Task DeletedExamSectionAsync(ExamEntity exam, List<SectionEntity> deletedSections)
        {
            if (deletedSections.Any())
            {
                var deletedSectionIds = deletedSections.Select(x => x.Id).ToList();
                // Delete exam section and questions
                var examSectionQuestions = await _examSectionQuestionRepository.GetListAsync(x => deletedSectionIds.Contains(x.SectionId));
                var deletedQuestionIds = examSectionQuestions.Select(x => x.QuestionId).ToList();
                await _examSectionQuestionRepository.DeleteAsync(x => deletedQuestionIds.Contains(x.SectionId));
                await _examSectionRepository.DeleteManyAsync(deletedSections);

                var examSectionGroupQuestions = await _examSectionGroupQuestionRepository.GetListAsync(x => deletedSectionIds.Contains(x.SectionId));
                var deletedGroupQuestionIds = examSectionGroupQuestions.Select(x => x.GroupQuestionId).ToList();
                await _examSectionGroupQuestionRepository.DeleteAsync(x => deletedGroupQuestionIds.Contains(x.GroupQuestionId));
            }
        }

        public async Task UpdateExamStatusAsync(Guid examId, UpdateStatusRequest updateStatusRequest)
        {
            // Lấy thông tin đề thi
            var exam = await _examRepository.GetAsync(examId);
            if (exam == null)
            {
                throw new UserFriendlyException("Exam not found");
            }

            // Cập nhật trạng thái đề thi
            exam.Status = updateStatusRequest.NewStatus;
            await _examRepository.UpdateAsync(exam);

            // Lấy danh sách các câu hỏi thuộc đề thi và không bật đồng bộ
            var sectionIds = (await _examSectionRepository.GetQueryableAsync())
                .Where(x => x.ExamId == examId)
                .Select(s => s.Id);

            var sectionQuestions = (await _examSectionQuestionRepository.GetQueryableAsync())
                .Where(x => sectionIds.Contains(x.SectionId) && !x.SyncQuestion)
                .ToList();

            var questionIds = sectionQuestions.Select(x => x.QuestionId).ToList();
            var questions = (await _questionRepository.GetListAsync(x => questionIds.Contains(x.Id))).ToList();

            // Cập nhật trạng thái câu hỏi
            foreach (var question in questions)
            {
                question.Status = updateStatusRequest.NewStatus;
            }

            await _questionRepository.UpdateManyAsync(questions);
        }

    }

    public class SectionQuestionDto
    {
        public Guid Id { get; set; }
        public Guid SectionId { get; set; }
        public Guid QuestionId { get; set; }
        public int Order { get; set; }
        public float? Score { get; set; }
        public bool SyncQuestion { get; set; }
        public Guid? LastSyncQuestionId { get; set; }
    }
}