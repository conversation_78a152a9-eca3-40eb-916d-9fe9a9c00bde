import React, {
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
} from 'react';
import {
  Card,
  Input,
  Button,
  message,
  Form,
  Divider,
  Collapse,
  Row,
  Col,
  Typography,
} from 'antd';
import {
  CheckOutlined,
  DeleteOutlined,
  PlusOutlined,
  DeleteFilled,
  RedoOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  BaseQuestion,
  QuestionComponentBaseProps,
} from '../../../interfaces/quizs/questionBase';
import {
  MatchingQuestion,
  MatchingQuestionAnswer,
  MatchingItem,
} from '../../../interfaces/quizs/mapping.interface';
import { Guid } from 'guid-typescript';
import practiceLocalization, { quizLocalization } from '../localization';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import {
  ContentFormatType,
  MatchingItemType,
} from '../../../interfaces/exams/examEnums';
import BaseQuestionPreviewComponent from '../base/BaseQuestionPreviewComponent';
import { matchingCheckCorrectAnswer } from './matchingUtils';
import MatchingItemComponent from './components/MatchingItemComponent';
import CustomRichText from '@/components/common/elements/Text/RichTextEditorComponent/v0/CustomRichText';
import ErrorBoundary from 'antd/es/alert/ErrorBoundary';
import useResponsiveCanvas, {
  CanvasConnection,
} from './hook/useResponsiveCanvas';

const { TextArea } = Input;
const { Title } = Typography;

export interface MatchingComponentProps extends QuestionComponentBaseProps {
  question: MatchingQuestion;
  onComplete?: (questionId: string, answer?: MatchingQuestionAnswer[]) => void;
  allowManyTimes?: boolean;
}

const MatchingComponent: React.FC<MatchingComponentProps> = ({
  question,
  questionIndex,
  onComplete,
  configMode = false,
  disabled = false,
  options = {
    hideDeleteButton: false,
    hideSaveButton: false,
    hideFeedback: false,
  },
  showResult = false,
  isMarked = false,
  onToggleMark,
}) => {
  // States for interaction mode
  const [userAnswers, setUserAnswers] = useState<MatchingQuestionAnswer[]>([]);
  const [selectedLeftIndices, setSelectedLeftIndices] = useState<number[]>([]);
  const [selectedRightIndices, setSelectedRightIndices] = useState<number[]>(
    []
  );

  // Responsive canvas hook
  const {
    canvasRef,
    containerRef,
    isReady,
    addConnection,
    removeConnection,
    clearConnections,
    forceRedraw,
  } = useResponsiveCanvas({
    debounceMs: 16,
    enableHighDPI: true,
    connectionStyle: {
      lineWidth: 3,
      lineCap: 'round',
      lineJoin: 'round',
      shadowBlur: 4,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
    },
  });

  // Context and hooks
  const { handleEditQuestion: handleChangeQuestion, handleDeleteQuestion } =
    useContext(PracticeEngineContext);
  const { handleUpdateQuestion } = useUpdateQuestion();

  // Config mode states
  const [editedQuestion, setEditedQuestion] = useState<MatchingQuestion>({
    ...question,
  });
  const [form] = Form.useForm();

  // Get premise and response items
  const premiseItems = useMemo(() => {
    return (question.matchingItems || []).filter(
      (item) => item.type === MatchingItemType.Premise
    );
  }, [question.matchingItems]);

  const responseItems = useMemo(() => {
    return (question.matchingItems || []).filter(
      (item) => item.type === MatchingItemType.Response
    );
  }, [question.matchingItems]);

  // Check if question is answered and correct
  const isAnswered = useMemo(() => {
    return userAnswers.length > 0;
  }, [userAnswers]);

  const isCorrect = useMemo(() => {
    if (!showResult || !isAnswered) return false;
    const checkResult = matchingCheckCorrectAnswer(question, userAnswers);
    return checkResult === true;
  }, [showResult, isAnswered, question, userAnswers]);

  // Update canvas connections when userAnswers change
  const updateCanvasConnections = useCallback(() => {
    if (!isReady || !containerRef.current) return;

    // Clear existing connections
    clearConnections();

    // Add new connections based on userAnswers
    userAnswers.forEach((answer, index) => {
      const premiseIndex = premiseItems.findIndex(
        (item) => item.clientId === answer.premiseId
      );
      const responseIndex = responseItems.findIndex(
        (item) => item.clientId === answer.responseId
      );

      if (premiseIndex === -1 || responseIndex === -1) return;

      const leftElement = containerRef.current?.querySelector(
        `[data-side="left"][data-index="${premiseIndex}"]`
      ) as HTMLElement;
      const rightElement = containerRef.current?.querySelector(
        `[data-side="right"][data-index="${responseIndex}"]`
      ) as HTMLElement;

      if (leftElement && rightElement) {
        // Determine connection color based on mode and correctness
        let connectionColor = '#6366f1,#10b981'; // Default gradient

        if (showResult) {
          // In result mode, check if this specific connection is correct
          const isConnectionCorrect = question.matchingAnswers?.some(
            (correctAnswer) =>
              correctAnswer.premiseId === answer.premiseId &&
              correctAnswer.responseId === answer.responseId
          );

          connectionColor = isConnectionCorrect
            ? '#10b981,#059669' // Green gradient for correct
            : '#ef4444,#dc2626'; // Red gradient for incorrect
        }

        const connection: CanvasConnection = {
          id: `${answer.premiseId}-${answer.responseId}-${index}`,
          startElement: leftElement,
          endElement: rightElement,
          color: connectionColor,
          animated: false,
        };

        addConnection(connection);
      }
    });
  }, [
    isReady,
    userAnswers,
    premiseItems,
    responseItems,
    question.matchingAnswers,
    showResult,
    clearConnections,
    addConnection,
  ]);

  // Update connections when relevant data changes
  useEffect(() => {
    if (isReady) {
      // Use setTimeout to ensure DOM elements are positioned
      setTimeout(updateCanvasConnections, 0);
    }
  }, [updateCanvasConnections, isReady]);

  // Handle left item click
  const handleLeftItemClick = (index: number) => {
    if (disabled || showResult) return;

    setSelectedLeftIndices((prev) => {
      if (prev.includes(index)) {
        return prev.filter((i) => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // Handle right item click
  const handleRightItemClick = (index: number) => {
    if (disabled || showResult) return;

    const responseItem = responseItems[index];

    if (!responseItem) return;

    // If left items are selected, create connections
    if (selectedLeftIndices.length > 0) {
      const newAnswers = [...userAnswers];

      selectedLeftIndices.forEach((leftIndex) => {
        const premiseItem = premiseItems[leftIndex];
        if (!premiseItem) return;

        // Check if connection already exists
        const existingIndex = newAnswers.findIndex(
          (answer) =>
            answer.premiseId === premiseItem.clientId &&
            answer.responseId === responseItem.clientId
        );

        if (existingIndex === -1) {
          // Add new connection
          newAnswers.push({
            id: undefined,
            clientId: Guid.create().toString(),
            premiseId: premiseItem.clientId,
            responseId: responseItem.clientId,
            content: '',
            contentFormat: ContentFormatType.Html,
            order: newAnswers.length,
            isCorrect: true,
          });
        }
      });

      setUserAnswers(newAnswers);
      setSelectedLeftIndices([]);

      // Notify parent component
      if (onComplete) {
        onComplete(question.clientId, newAnswers);
      }
    } else {
      // Toggle right selection
      setSelectedRightIndices((prev) => {
        if (prev.includes(index)) {
          return prev.filter((i) => i !== index);
        } else {
          return [...prev, index];
        }
      });
    }
  };

  // Handle connection removal
  const handleRemoveConnection = (
    premiseIndex: number,
    connectionIndex: number
  ) => {
    if (disabled || showResult) return;

    const premiseItem = premiseItems[premiseIndex];
    if (!premiseItem) return;

    // Find connections for this premise
    const premiseConnections = userAnswers.filter(
      (answer) => answer.premiseId === premiseItem.clientId
    );

    if (connectionIndex < premiseConnections.length) {
      const connectionToRemove = premiseConnections[connectionIndex];
      const newAnswers = userAnswers.filter(
        (answer) => answer.clientId !== connectionToRemove.clientId
      );

      setUserAnswers(newAnswers);

      if (onComplete) {
        onComplete(question.clientId, newAnswers);
      }
    }
  };

  // Reset all connections
  const handleResetConnections = () => {
    if (disabled || showResult) return;

    setUserAnswers([]);
    setSelectedLeftIndices([]);
    setSelectedRightIndices([]);

    if (onComplete) {
      onComplete(question.clientId, []);
    }
  };

  // Get matched labels for items
  const getMatchedLabels = (
    index: number,
    side: 'left' | 'right'
  ): string[] => {
    if (side === 'left') {
      const premiseItem = premiseItems[index];
      if (!premiseItem) return [];

      const matchedResponses = userAnswers
        .filter((answer) => answer.premiseId === premiseItem.clientId)
        .map((answer) => {
          const responseIndex = responseItems.findIndex(
            (item) => item.clientId === answer.responseId
          );
          return String.fromCharCode(65 + responseIndex);
        });

      return matchedResponses;
    } else {
      const responseItem = responseItems[index];
      if (!responseItem) return [];

      const matchedPremises = userAnswers
        .filter((answer) => answer.responseId === responseItem.clientId)
        .map((answer) => {
          const premiseIndex = premiseItems.findIndex(
            (item) => item.clientId === answer.premiseId
          );
          return `${premiseIndex + 1}`;
        });

      return matchedPremises;
    }
  };

  // Check if item is connected correctly in result mode
  const isItemCorrect = (itemId: string, side: 'left' | 'right'): boolean => {
    if (!showResult) return false;

    const correctAnswers = question.matchingAnswers || [];
    const userConnections = userAnswers;

    if (side === 'left') {
      // Check if all correct connections for this premise are present
      const correctForPremise = correctAnswers.filter(
        (answer) => answer.premiseId === itemId
      );
      const userForPremise = userConnections.filter(
        (answer) => answer.premiseId === itemId
      );

      return (
        correctForPremise.every((correct) =>
          userForPremise.some((user) => user.responseId === correct.responseId)
        ) &&
        userForPremise.every((user) =>
          correctForPremise.some(
            (correct) => correct.responseId === user.responseId
          )
        )
      );
    } else {
      // Check if all correct connections for this response are present
      const correctForResponse = correctAnswers.filter(
        (answer) => answer.responseId === itemId
      );
      const userForResponse = userConnections.filter(
        (answer) => answer.responseId === itemId
      );

      return (
        correctForResponse.every((correct) =>
          userForResponse.some((user) => user.premiseId === correct.premiseId)
        ) &&
        userForResponse.every((user) =>
          correctForResponse.some(
            (correct) => correct.premiseId === user.premiseId
          )
        )
      );
    }
  };

  // Initialize user answers from saved state
  useEffect(() => {
    if (question.userSelect && Array.isArray(question.userSelect)) {
      setUserAnswers(question.userSelect);
    } else {
      setUserAnswers([]);
    }
  }, [question.clientId, question.userSelect]);

  // Force redraw when component mounts or important props change
  useEffect(() => {
    if (isReady) {
      // Delay to ensure DOM is fully rendered
      const timer = setTimeout(() => {
        forceRedraw();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isReady, forceRedraw, premiseItems.length, responseItems.length]);

  // Config mode handlers
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updatedQuestion = {
      ...editedQuestion,
      title: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleContentChange = (value: string) => {
    const updatedQuestion = {
      ...editedQuestion,
      content: value,
      description: value,
    };
    setEditedQuestion(updatedQuestion);
  };

  const handleContentBlur = () => {
    handleUpdateQuestion(editedQuestion as BaseQuestion);
  };

  const handleExplanationChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const updatedQuestion = {
      ...editedQuestion,
      explanation: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const addNewPremiseItem = () => {
    const newItem: MatchingItem = {
      clientId: Guid.create().toString(),
      content: 'Mục mới',
      type: MatchingItemType.Premise,
      order: premiseItems.length,
    };

    const updatedQuestion = {
      ...editedQuestion,
      matchingItems: [...(editedQuestion.matchingItems || []), newItem],
    };

    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const addNewResponseItem = () => {
    const newItem: MatchingItem = {
      clientId: Guid.create().toString(),
      content: 'Mục mới',
      type: MatchingItemType.Response,
      order: responseItems.length,
    };

    const updatedQuestion = {
      ...editedQuestion,
      matchingItems: [...(editedQuestion.matchingItems || []), newItem],
    };

    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const removeItem = (itemId: string) => {
    const updatedItems = (editedQuestion.matchingItems || []).filter(
      (item) => item.clientId !== itemId
    );
    const updatedAnswers = (editedQuestion.matchingAnswers || []).filter(
      (answer) => answer.premiseId !== itemId && answer.responseId !== itemId
    );

    const updatedQuestion = {
      ...editedQuestion,
      matchingItems: updatedItems,
      matchingAnswers: updatedAnswers,
    };

    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const updateItemContent = (itemId: string, content: string) => {
    const updatedItems = (editedQuestion.matchingItems || []).map((item) =>
      item.clientId === itemId ? { ...item, content } : item
    );

    const updatedQuestion = {
      ...editedQuestion,
      matchingItems: updatedItems,
    };

    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleSaveConfig = () => {
    const hasTitle = editedQuestion.title.trim() !== '';
    const hasContent = editedQuestion.content?.trim() !== '';
    const hasPremiseItems = premiseItems.length > 0;
    const hasResponseItems = responseItems.length > 0;
    const allItemsHaveContent = (editedQuestion.matchingItems || []).every(
      (item) => item.content.trim() !== ''
    );

    if (!hasTitle) {
      message.error('Tiêu đề không được để trống!');
      return;
    }

    if (!hasContent) {
      message.error('Nội dung câu hỏi không được để trống!');
      return;
    }

    if (!hasPremiseItems || !hasResponseItems) {
      message.error('Phải có ít nhất một mục trong mỗi cột!');
      return;
    }

    if (!allItemsHaveContent) {
      message.error('Tất cả các mục phải có nội dung!');
      return;
    }

    handleChangeQuestion({
      ...editedQuestion,
      parentId: question.parentId,
    });
    message.success('Lưu cấu hình thành công!');
  };

  const deleteConfirm = () => {
    handleDeleteQuestion(question.clientId);
  };

  // Initialize edited question
  useEffect(() => {
    setEditedQuestion({ ...question });
  }, [question.clientId]);

  // Config mode render
  if (configMode) {
    const currentPremiseItems = (editedQuestion.matchingItems || []).filter(
      (item) => item.type === MatchingItemType.Premise
    );
    const currentResponseItems = (editedQuestion.matchingItems || []).filter(
      (item) => item.type === MatchingItemType.Response
    );

    const collapseItems = [
      {
        key: '1',
        label: practiceLocalization['Advanced Options'],
        children: (
          <Form.Item label="Giải thích đáp án">
            <TextArea
              rows={3}
              value={editedQuestion.explanation || ''}
              onChange={handleExplanationChange}
              placeholder="Nhập giải thích cho đáp án"
            />
          </Form.Item>
        ),
      },
    ];

    return (
      <Card>
        <Form form={form} className="form-config" layout="vertical">
          <Form.Item required hidden>
            <Input
              hidden
              variant="underlined"
              value={editedQuestion.title}
              onChange={handleTitleChange}
              placeholder="Nhập tiêu đề câu hỏi"
              className="tailwind-font-medium tailwind-text-lg"
            />
          </Form.Item>

          <Form.Item label="Nội dung câu hỏi" required>
            <ErrorBoundary>
              <CustomRichText
                height={200}
                value={editedQuestion.content}
                onChangeValue={(_name, value) => {
                  handleContentChange(value || '');
                }}
                handleBur={handleContentBlur}
                toolbarSettingItems={[
                  'Bold',
                  'Italic',
                  'Underline',
                  'OrderedList',
                  'UnorderedList',
                  'Image',
                  'CreateLink',
                ]}
              />
            </ErrorBoundary>
          </Form.Item>

          <Divider>Cấu hình ghép cặp</Divider>

          <Row gutter={16}>
            {/* Cột 1 - Premise Items */}
            <Col span={12}>
              <div className="tailwind-mb-4">
                <div className="tailwind-flex tailwind-items-center tailwind-mb-2">
                  <Title level={5} className="tailwind-font-medium">
                    Cột 1
                  </Title>
                </div>
                {currentPremiseItems.map((item, index) => (
                  <div key={item.clientId} className="tailwind-mb-3">
                    <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                      <span className="tailwind-text-sm tailwind-font-medium tailwind-text-indigo-600">
                        {index + 1}.
                      </span>
                      <Input
                        value={item.content}
                        onChange={(e) =>
                          updateItemContent(item.clientId, e.target.value)
                        }
                        placeholder={`Mục ${index + 1}`}
                        variant="underlined"
                      />
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => removeItem(item.clientId)}
                        disabled={currentPremiseItems.length <= 1}
                      />
                    </div>
                  </div>
                ))}
                <div className="tailwind-flex  tailwind-items-center tailwind-mb-2">
                  <Button
                    type="dashed"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={addNewPremiseItem}
                  >
                    Thêm mục
                  </Button>
                </div>
              </div>
            </Col>

            {/* Cột 2 - Response Items */}
            <Col span={12}>
              <div className="tailwind-mb-4">
                <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
                  <Title level={5} className="tailwind-font-medium">
                    Cột 2
                  </Title>
                </div>
                {currentResponseItems.map((item, index) => (
                  <div key={item.clientId} className="tailwind-mb-3">
                    <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                      <span className="tailwind-text-sm tailwind-font-medium tailwind-text-green-600">
                        {String.fromCharCode(65 + index)}.
                      </span>
                      <Input
                        value={item.content}
                        onChange={(e) =>
                          updateItemContent(item.clientId, e.target.value)
                        }
                        placeholder={`Mục ${String.fromCharCode(65 + index)}`}
                        variant="underlined"
                      />
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => removeItem(item.clientId)}
                        disabled={currentResponseItems.length <= 1}
                      />
                    </div>
                  </div>
                ))}
                <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
                  <Button
                    type="dashed"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={addNewResponseItem}
                  >
                    Thêm mục
                  </Button>
                </div>
              </div>
            </Col>
          </Row>

          {!options.hideSaveButton && (
            <Form.Item>
              <Button
                type="primary"
                onClick={handleSaveConfig}
                className="flex-1 bg-blue-500 hover:bg-blue-600"
                icon={<CheckOutlined />}
                block
              >
                Lưu cấu hình
              </Button>
            </Form.Item>
          )}

          <Collapse defaultActiveKey={[]} ghost items={collapseItems} />

          {!options.hideDeleteButton && (
            <PopconfirmAntdCustom
              title={quizLocalization.buttons.deleteQuestion.confirmTitle}
              onConfirm={deleteConfirm}
              onCancel={() => {}}
              okText={quizLocalization.buttons.deleteQuestion.yes}
              cancelText={quizLocalization.buttons.deleteQuestion.no}
            >
              <Button danger icon={<DeleteFilled />}>
                {quizLocalization.buttons.deleteQuestion.button}
              </Button>
            </PopconfirmAntdCustom>
          )}
        </Form>
      </Card>
    );
  }

  // Preview/Result mode render
  const renderContent = () => {
    return (
      <div className="matching-question-preview">
        <div dangerouslySetInnerHTML={{ __html: question.content || '' }} />
      </div>
    );
  };

  const renderInteraction = () => {
    if (premiseItems.length === 0 || responseItems.length === 0) {
      return (
        <div className="tailwind-text-center tailwind-py-8 tailwind-text-gray-500">
          Câu hỏi chưa được cấu hình đầy đủ
        </div>
      );
    }

    return (
      <div
        ref={containerRef}
        className="tailwind-bg-gray-50 tailwind-p-4 tailwind-rounded-lg tailwind-border tailwind-border-gray-200 tailwind-relative tailwind-min-h-[400px]"
      >
        {/* Canvas for drawing connections */}
        <canvas
          ref={canvasRef}
          className="tailwind-absolute tailwind-inset-0 tailwind-pointer-events-none tailwind-z-10"
          style={{ pointerEvents: 'none' }}
        />

        {/* Selection status and actions */}
        {!showResult && (
          <div className="tailwind-flex tailwind-justify-between tailwind-mb-2 tailwind-relative tailwind-z-20 tailwind-items-center">
            <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
              <Button
                icon={<RedoOutlined />}
                onClick={handleResetConnections}
                disabled={disabled || userAnswers.length === 0}
                size="small"
                title="Đặt lại toàn bộ kết nối"
              >
                Đặt lại
              </Button>
            </div>
          </div>
        )}

        {/* Matching items container */}
        <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-10 tailwind-relative tailwind-z-20">
          {/* Left column - Premise items */}
          <div>
            {premiseItems.map((item, index) => {
              const isSelected = selectedLeftIndices.includes(index);
              const isMatched = userAnswers.some(
                (answer) => answer.premiseId === item.clientId
              );
              const itemCorrect = showResult
                ? isItemCorrect(item.clientId, 'left')
                : undefined;

              return (
                <MatchingItemComponent
                  key={item.clientId}
                  index={index}
                  originalIndex={index}
                  text={item.content}
                  isSelected={isSelected}
                  isMatched={isMatched}
                  matchedItemLabels={getMatchedLabels(index, 'left')}
                  onClick={() => handleLeftItemClick(index)}
                  onRemove={(connectionIdx) =>
                    handleRemoveConnection(index, connectionIdx)
                  }
                  side="left"
                  disabled={disabled}
                  showResult={showResult}
                  isCorrect={itemCorrect}
                />
              );
            })}
          </div>

          {/* Right column - Response items */}
          <div>
            {responseItems.map((item, index) => {
              if (!item) return null;

              const isSelected = selectedRightIndices.includes(index);
              const isMatched = userAnswers.some(
                (answer) => answer.responseId === item.clientId
              );
              const itemCorrect = showResult
                ? isItemCorrect(item.clientId, 'right')
                : undefined;

              return (
                <MatchingItemComponent
                  key={item.clientId}
                  index={index}
                  originalIndex={index}
                  text={item.content}
                  isSelected={isSelected}
                  isMatched={isMatched}
                  matchedItemLabels={getMatchedLabels(index, 'right')}
                  onClick={() => handleRightItemClick(index)}
                  onRemove={(connectionIdx) => {
                    // Find and remove the specific connection
                    const connections = userAnswers.filter(
                      (answer) => answer.responseId === item.clientId
                    );
                    if (connectionIdx < connections.length) {
                      const connectionToRemove = connections[connectionIdx];
                      const newAnswers = userAnswers.filter(
                        (answer) =>
                          answer.clientId !== connectionToRemove.clientId
                      );
                      setUserAnswers(newAnswers);
                      if (onComplete) {
                        onComplete(question.clientId, newAnswers);
                      }
                    }
                  }}
                  side="right"
                  disabled={disabled}
                  showResult={showResult}
                  isCorrect={itemCorrect}
                />
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const renderCorrectAnswer = () => {
    if (!showResult || isCorrect || !question.matchingAnswers) return null;

    const correctAnswerText = question.matchingAnswers
      .map((answer) => {
        const premiseIndex = premiseItems.findIndex(
          (item) => item.clientId === answer.premiseId
        );
        const responseIndex = responseItems.findIndex(
          (item) => item.clientId === answer.responseId
        );

        if (premiseIndex === -1 || responseIndex === -1) return null;

        return `${premiseIndex + 1}-${String.fromCharCode(65 + responseIndex)}`;
      })
      .filter(Boolean)
      .join(', ');

    return (
      <div className="correct-answer-display tailwind-mt-2">
        <div className="answer-content tailwind-italic">
          {correctAnswerText}
        </div>
      </div>
    );
  };

  return (
    <BaseQuestionPreviewComponent
      question={question}
      questionIndex={questionIndex}
      hideFeedback={options.hideFeedback}
      showResult={showResult}
      isCorrect={isCorrect}
      isAnswered={isAnswered}
      cardClassName="matching-card"
      guidanceText="Ghép các mục ở cột 1 với các mục tương ứng ở cột 2 bằng cách chọn và kết nối chúng."
      renderContent={renderContent}
      renderInteraction={renderInteraction}
      renderCorrectAnswer={renderCorrectAnswer}
      isMarked={isMarked}
      onToggleMark={onToggleMark}
    />
  );
};

export default MatchingComponent;
