﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Volo.Abp.Domain.Values;

namespace EdTech.Study.Exams
{
    public class ItemOrder : ValueObject
    {
        [Required]
        public Guid ItemId { get; private set; }

        public int Order { get; private set; }

        // Required for EF Core
        private ItemOrder()
        {
        }

        public ItemOrder(Guid itemId, int order)
        {
            ItemId = itemId;
            Order = order;
        }

        // Define which properties determine equality
        protected override IEnumerable<object> GetAtomicValues()
        {
            yield return ItemId;
            yield return Order;
        }
    }
}
