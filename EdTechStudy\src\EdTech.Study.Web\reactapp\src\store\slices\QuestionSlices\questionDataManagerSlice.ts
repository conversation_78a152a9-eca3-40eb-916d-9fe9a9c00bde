import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  BaseQuestion,
  getExamStatusFromString,
  QuestionType,
} from '../../../interfaces/quizs/questionBase';
import questionDraft<PERSON><PERSON>, {
  QuestionDraftParams,
  QuestionDraft,
  BatchAssignQuestionsDto,
} from '../../../api/questionDraftApi';
import { ExamStatus } from '../../../interfaces/exams/examEnums';
import { IQuestion } from '../../../interfaces/questions/question';
import { convertKeysToLowerCamelCase } from '@tsp/utils';
import {
  getQuestionTypeFromString,
  getQuestionTypeString,
  getSourceTypeFromString,
} from '../../../utils/questionUtil';
import { getExamStatusString } from '../../../utils/examUtils';
import {
  ExamBase,
  ExamGroupQuestion,
} from '../../../interfaces/exams/examBase';

export interface QuestionDataManagerState {
  questionData: BaseQuestion[] | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  pagination: {
    current: number;
    pageSize: number;
  };
  filters: {
    type?: QuestionType | QuestionType[];
    status?: ExamStatus | ExamStatus[];
    subjectIds?: string[];
    searchText?: string;
    assignedUserIds?: string[];
  };
  currentQuestion: IQuestion | null;
  // Store exam info for each question and by exam ID
  examInfoCache: Record<string, ExamBase>; // Store exams by ID
  // Store group info for each question and by group ID
  groupInfoCache: Record<string, ExamGroupQuestion>; // Store groups by ID
  // Track which questions belong to which groups
  questionToGroupMap: Record<string, string>; // questionId -> groupId
  // Track which questions belong to which exams
  questionToExamMap: Record<string, string[]>; // questionId -> examIds[]
}

export const QUESTION_DATA_SLICE_NAME = 'questionDataManager';

// Add the async thunk for batch assigning questions to a user
export const batchAssignQuestionsToUser = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/batchAssignQuestionsToUser`,
  async (assignData: BatchAssignQuestionsDto, { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.batchAssignQuestionsToUser(
        assignData
      );

      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(
          response.error || 'Failed to assign questions to user'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Add the async thunk for batch unassigning questions
export const batchUnassignQuestions = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/batchUnassignQuestions`,
  async (questionIds: string[], { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.batchUnassignQuestions(
        questionIds
      );

      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(
          response.error || 'Failed to unassign questions'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Async thunk for fetching questions using OData
export const fetchQuestions = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/fetchQuestions`,
  async (params: QuestionDraftParams, { rejectWithValue }) => {
    try {
      // Format sort parameters for OData query
      let formattedParams = { ...params };

      // Translate Ant Design sort order to OData format
      if (params.sortField && params.sortOrder) {
        formattedParams.sortField = params.sortField;
        // Transform sortOrder from Ant Design format to OData format
        formattedParams.sortOrder =
          params.sortOrder === 'ascend' ? 'asc' : 'desc';

        // Store sort information in state
        formattedParams.sortField = formattedParams.sortField || 'creationTime';
        formattedParams.sortOrder = formattedParams.sortOrder || 'desc';
      } else {
        formattedParams.sortField = 'creationTime';
        formattedParams.sortOrder = 'desc';
      }

      // replace sort key statusEntity to status
      if (formattedParams.sortField === 'statusEntity') {
        formattedParams.sortField = 'status';
      }

      const response = await questionDraftApi.getQuestions(formattedParams);
      if (response.success) {
        return {
          data: response.data.map((question: any) => ({
            ...question,
            questionType: getQuestionTypeFromString(
              question.questionType ?? ''
            ),
            statusEntity: getExamStatusFromString(question.status ?? ''),
            statusEntityString: question.status ?? '',
            sourceType: getSourceTypeFromString(question.sourceType ?? ''),
          })),
          totalCount: response.total,
          pagination: {
            current: params.page || 1,
            pageSize: params.pageSize || 10,
          },
          filters: {
            type: params.questionType,
            status: params.status,
            subjectIds: params.subjectIds,
            searchText: params.searchText,
            lessonGradeIds: params.lessonGradeIds,
            assignedUserIds: params.assignedUserIds || undefined,
          },
        };
      } else {
        return rejectWithValue(response.error || 'Failed to fetch questions');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// export const getPaginationInfo = createAsyncThunk(
//   `${QUESTION_DATA_SLICE_NAME}/getPaginationInfo`,
//   async () => {
//     try {
//       const response = await questionDraftApi.getPaginationInfo();

//       if (response.success) {
//         return {
//           data: response.data,
//         };
//       }
//     } catch (error) {}
//   }
// );

export const updateQuestions = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/updateQuestions`,
  async (questions: QuestionDraft[], { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.updateQuestions(questions);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update questions');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const createQuestion = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/createQuestion`,
  async (question: IQuestion, { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.createQuestion(question);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to create question');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const updateQuestion = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/updateQuestion`,
  async (question: IQuestion, { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.updateQuestion(question);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update question');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const deleteQuestions = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/deleteQuestions`,
  async (questionIds: string[], { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.deleteQuestions(questionIds);
      if (response.success) {
        return questionIds;
      } else {
        return rejectWithValue(response.error || 'Failed to delete questions');
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const fetchQuestionInfo = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/fetchQuestionInfo`,
  async (questionId: string, { rejectWithValue }) => {
    try {
      const response = await questionDraftApi.getQuestionInfo(questionId);
      if (response.success) {
        let dataMap = convertKeysToLowerCamelCase(response.data) as IQuestion;
        dataMap.clientId = response.data.id;
        dataMap.questionType = getQuestionTypeFromString(
          response.data.QuestionType ?? ''
        );
        dataMap.type = getQuestionTypeString(dataMap.questionType);
        dataMap.statusEntity = getExamStatusFromString(
          response.data.Status ?? ''
        );
        dataMap.statusEntityString = response.data.Status ?? '';
        dataMap.sourceType = getSourceTypeFromString(
          response.data.SourceType ?? ''
        );

        // options
        if (
          dataMap.options &&
          Array.isArray(dataMap.options) &&
          dataMap.options.length > 0
        ) {
          dataMap.options = dataMap.options.map((option) => ({
            ...option,
            clientId: option.id || option.clientId,
          }));
        }

        // fillInBlankAnswers
        if (
          dataMap.fillInBlankAnswers &&
          Array.isArray(dataMap.fillInBlankAnswers) &&
          dataMap.fillInBlankAnswers.length > 0
        ) {
          dataMap.fillInBlankAnswers = dataMap.fillInBlankAnswers.map(
            (answer) => ({
              ...answer,
              clientId: answer.id || answer.clientId,
            })
          );
        }

        // matchingItems
        if (
          dataMap.matchingItems &&
          Array.isArray(dataMap.matchingItems) &&
          dataMap.matchingItems.length > 0
        ) {
          dataMap.matchingItems = dataMap.matchingItems.map((item) => ({
            ...item,
            clientId: item.id || item.clientId,
          }));
        }

        // matchingAnswers
        if (
          dataMap.matchingAnswers &&
          Array.isArray(dataMap.matchingAnswers) &&
          dataMap.matchingAnswers.length > 0
        ) {
          dataMap.matchingAnswers = dataMap.matchingAnswers.map((answer) => ({
            ...answer,
            clientId: answer.id || answer.clientId,
          }));
        }

        return {
          data: dataMap,
        };
      } else {
        return rejectWithValue(
          response.error || 'Failed to fetch question information'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Thêm async thunk mới để cập nhật trạng thái câu hỏi
export const updateQuestionStatus = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/updateQuestionStatus`,
  async (
    { questionId, status }: { questionId: string; status: ExamStatus },
    { rejectWithValue }
  ) => {
    try {
      const response = await questionDraftApi.updateQuestionStatus(
        questionId,
        status
      );
      if (response.success) {
        return {
          id: questionId,
          status: status,
          data: response.data,
        };
      } else {
        return rejectWithValue(
          response.error || 'Failed to update question status'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Thêm async thunk để cập nhật trạng thái nhiều câu hỏi cùng lúc
export const updateQuestionsStatus = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/updateQuestionsStatus`,
  async (
    { questionIds, status }: { questionIds: string[]; status: ExamStatus },
    { rejectWithValue }
  ) => {
    try {
      const response = await questionDraftApi.updateQuestionsStatus(
        questionIds,
        status
      );
      if (response.success) {
        return {
          ids: questionIds,
          status: status,
          data: response.data,
        };
      } else {
        return rejectWithValue(
          response.error || 'Failed to update questions status'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Thêm async thunk để lấy thông tin đề thi dựa vào questionId
export const fetchExamInfoByQuestionId = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/fetchExamInfoByQuestionId`,
  async (questionId: string, { getState, rejectWithValue }) => {
    // Get the current state
    const state = getState() as {
      questionDataManager: QuestionDataManagerState;
    };

    // Check if we know which exams this question belongs to
    const examIds = state.questionDataManager.questionToExamMap[questionId];
    if (examIds && examIds.length > 0) {
      // Get all exams from cache
      const cachedExams = examIds
        .map((id) => state.questionDataManager.examInfoCache[id])
        .filter(Boolean);

      // If we have all exams in cache
      if (cachedExams.length === examIds.length) {
        return {
          questionId,
          exams: cachedExams,
          fromCache: true,
        };
      }
    }

    // If not in cache, fetch from API
    try {
      const response = await questionDraftApi.getExamInfoByQuestionId(
        questionId
      );
      if (response.success) {
        return {
          questionId,
          exams: response.data,
          fromCache: false,
        };
      } else {
        return rejectWithValue(
          response.error || 'Failed to fetch exam information for question'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Add this async thunk
export const fetchGroupInfoByQuestionId = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/fetchGroupInfoByQuestionId`,
  async (questionId: string, { getState, rejectWithValue }) => {
    // Get the current state
    const state = getState() as {
      questionDataManager: QuestionDataManagerState;
    };

    // Check if we know which group this question belongs to
    const groupId = state.questionDataManager.questionToGroupMap[questionId];
    if (groupId && state.questionDataManager.groupInfoCache[groupId]) {
      // We already have this group in cache
      return {
        questionId,
        groupInfo: state.questionDataManager.groupInfoCache[groupId],
        fromCache: true,
      };
    }

    // If not in cache, fetch from API
    try {
      const response = await questionDraftApi.getGroupInfoByQuestionId(
        questionId
      );
      if (response.success) {
        return {
          questionId,
          groupInfo: response.data,
          fromCache: false,
        };
      } else {
        return rejectWithValue(
          response.error || 'Failed to fetch group information for question'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Add this async thunk for updating group info
export const updateGroupInfo = createAsyncThunk(
  `${QUESTION_DATA_SLICE_NAME}/updateGroupInfo`,
  async (
    groupInfo: {
      id: string;
      content?: string;
      instructions?: string;
      order?: number;
      questionId?: string; // Optional: to know which question's group we're updating
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await questionDraftApi.updateGroupInfo({
        id: groupInfo.id,
        content: groupInfo.content,
        instructions: groupInfo.instructions,
        order: groupInfo.order,
      });

      if (response.success) {
        return {
          groupInfo: response.data,
          questionId: groupInfo.questionId,
        };
      } else {
        return rejectWithValue(
          response.error || 'Failed to update group information'
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Add this helper function to update the cache and mappings
const updateCacheAndMappings = (
  state: QuestionDataManagerState,
  questionId: string,
  groupInfo?: ExamGroupQuestion,
  examInfo?: ExamBase[]
) => {
  // Update group cache and mappings
  if (groupInfo) {
    // Store in cache by ID
    if (groupInfo.id !== undefined) {
      state.groupInfoCache[groupInfo.id] = groupInfo;
    }

    // Update the mapping from question to group
    if (groupInfo.id !== undefined) {
      state.questionToGroupMap[questionId] = groupInfo.id;
    }

    // Update the mapping for all questions in this group
    if (groupInfo.questions) {
      groupInfo.questions.forEach((question) => {
        if (question.id || question.clientId) {
          const qId = question.id || question.clientId;
          if (groupInfo.id !== undefined) {
            state.questionToGroupMap[qId] = groupInfo.id;
          }
        }
      });
    }
  }

  // Update exam cache and mappings
  if (examInfo && examInfo.length > 0) {
    // Store each exam in cache by ID
    examInfo.forEach((exam) => {
      if (exam.id !== undefined) {
        state.examInfoCache[exam.id] = exam;
      }
    });

    // Update the mapping from question to exams
    state.questionToExamMap[questionId] = examInfo
      .map((exam) => exam.id)
      .filter((id): id is string => typeof id === 'string');

    // For each exam, we could also update all questions that belong to it
    // This would require traversing the exam structure, which might be complex
    // Implement if needed based on your application's requirements
  }
};

// Add these helper functions to get data from the state
// Helper function to get exams for a question
export const getExamsForQuestion = (
  state: QuestionDataManagerState,
  questionId: string
): ExamBase[] => {
  const examIds = state.questionToExamMap[questionId] || [];
  return examIds.map((examId) => state.examInfoCache[examId]).filter(Boolean);
};

// Helper function to get group for a question
export const getGroupForQuestion = (
  state: QuestionDataManagerState,
  questionId: string
): ExamGroupQuestion | undefined => {
  const groupId = state.questionToGroupMap[questionId];
  return groupId ? state.groupInfoCache[groupId] : undefined;
};

const questionDataManagerSlice = createSlice({
  name: QUESTION_DATA_SLICE_NAME,
  initialState: {
    questionData: null,
    loading: false,
    error: null,
    totalCount: 0,
    pagination: {
      current: 1,
      pageSize: 10,
    },
    filters: {
      type: undefined,
      status: undefined,
      subjectIds: undefined,
      searchText: undefined,
      assignedUserIds: undefined,
    },
    currentQuestion: null,
    examInfoCache: {},
    groupInfoCache: {},
    questionToGroupMap: {},
    questionToExamMap: {},
  } as QuestionDataManagerState,
  reducers: {
    setQuestionData: (state, action) => {
      state.questionData = action.payload;
    },
    handleChangeQuestion: (state, action) => {
      const { id, question } = action.payload;
      const index = state.questionData?.findIndex((q) => q.clientId === id);
      if (index !== undefined && state.questionData && index !== -1) {
        state.questionData[index] = question;
      }
    },
    clearQuestionData: (state) => {
      state.questionData = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = {
        type: undefined,
        status: undefined,
        subjectIds: undefined,
        searchText: undefined,
        assignedUserIds: undefined,
      };
    },
  },
  extraReducers: (builder) => {
    // Handle fetchQuestions
    builder
      .addCase(fetchQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.questionData = action.payload.data;
        state.pagination = action.payload.pagination;
        state.filters = {
          ...action.payload.filters,
          assignedUserIds: action.payload.filters.assignedUserIds || undefined,
        };
        state.totalCount = action.payload.totalCount;
      })
      .addCase(fetchQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // .addCase(getPaginationInfo.fulfilled, (state, action) => {
      //   state.totalCount = action.payload?.data?.totalCount;
      // })
      .addCase(updateQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        if (Array.isArray(action.payload.data)) {
          state.questionData = action.payload.data;
        }
      })
      .addCase(updateQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(deleteQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        // Get the IDs of questions to remove
        const deletedIds = action.payload as string[];

        // Update the question data by filtering out deleted questions
        if (state.questionData) {
          state.questionData = state.questionData.filter(
            (question) => !deletedIds.includes(question.clientId)
          );
        }

        // Update the total count
        if (state.totalCount) {
          state.totalCount = Math.max(0, state.totalCount - deletedIds.length);
        }
      })
      .addCase(deleteQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchQuestionInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuestionInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.currentQuestion = action.payload.data as IQuestion;
      })
      .addCase(fetchQuestionInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(createQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        if (state.questionData && action.payload) {
          state.questionData = [...state.questionData, action.payload];
        }
        if (state.totalCount) {
          state.totalCount += 1;
        }
      })
      .addCase(createQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        if (state.questionData && action.payload) {
          const index = state.questionData.findIndex(
            (q) =>
              q.clientId === action.payload.id || q.id === action.payload.id
          );
          if (index !== -1) {
            state.questionData[index] = action.payload;
          }
        }
      })
      .addCase(updateQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateQuestionStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateQuestionStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        // Cập nhật trạng thái câu hỏi trong danh sách
        if (state.questionData) {
          const index = state.questionData.findIndex(
            (question) =>
              question.id === action.payload.id ||
              question.clientId === action.payload.id
          );

          if (index !== -1) {
            state.questionData[index] = {
              ...state.questionData[index],
              statusEntity: action.payload.status,
              statusEntityString: getExamStatusString(action.payload.status),
              status: getExamStatusString(action.payload.status),
            };
          }
        }

        // Nếu đang xem chi tiết câu hỏi này, cập nhật luôn
        if (
          state.currentQuestion &&
          (state.currentQuestion.id === action.payload.id ||
            state.currentQuestion.clientId === action.payload.id)
        ) {
          state.currentQuestion = {
            ...state.currentQuestion,
            statusEntity: action.payload.status,
            statusEntityString: getExamStatusString(action.payload.status),
            status: getExamStatusString(action.payload.status),
          };
        }
      })
      .addCase(updateQuestionStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateQuestionsStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateQuestionsStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        // Update the status of all affected questions in the list
        if (state.questionData) {
          state.questionData = state.questionData.map((question) => {
            // Check if this question is in the updated list
            if (
              action.payload.ids.includes(question.id || '') ||
              action.payload.ids.includes(question.clientId)
            ) {
              return {
                ...question,
                statusEntity: action.payload.status,
                statusEntityString: getExamStatusString(action.payload.status),
                status: getExamStatusString(action.payload.status),
              };
            }
            return question;
          });
        }

        // If the current question is one of the updated ones, update it too
        if (
          state.currentQuestion &&
          (action.payload.ids.includes(state.currentQuestion.id || '') ||
            action.payload.ids.includes(state.currentQuestion.clientId))
        ) {
          state.currentQuestion = {
            ...state.currentQuestion,
            statusEntity: action.payload.status,
            statusEntityString: getExamStatusString(action.payload.status),
            status: getExamStatusString(action.payload.status),
          };
        }
      })
      .addCase(updateQuestionsStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchExamInfoByQuestionId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExamInfoByQuestionId.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        // Only update cache if this is new data from API
        if (!action.payload.fromCache) {
          updateCacheAndMappings(
            state,
            action.payload.questionId,
            undefined,
            action.payload.exams
          );
        }
      })
      .addCase(fetchExamInfoByQuestionId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchGroupInfoByQuestionId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGroupInfoByQuestionId.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        if (!action.payload.fromCache) {
          updateCacheAndMappings(
            state,
            action.payload.questionId,
            action.payload.groupInfo
          );
        }
      })
      .addCase(fetchGroupInfoByQuestionId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateGroupInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateGroupInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        const updatedGroup = action.payload.groupInfo;

        // Update the group in cache
        if (updatedGroup && updatedGroup.id) {
          state.groupInfoCache[updatedGroup.id] = updatedGroup;

          // Update all question mappings for this group
          if (updatedGroup.questions) {
            updatedGroup.questions.forEach((question: any) => {
              const qId = question.id || question.clientId;
              if (qId) {
                state.questionToGroupMap[qId] = updatedGroup.id;
              }
            });
          }
        }

        // If the updated group contains questions that are in our current question list,
        // update those questions to reflect they belong to this group
        if (state.questionData && updatedGroup.questions) {
          const questionIds = updatedGroup.questions.map((q: any) => q.id);
          state.questionData = state.questionData.map((question) => {
            if (questionIds.includes(question.id)) {
              return {
                ...question,
                groupQuestionId: updatedGroup.id,
              };
            }
            return question;
          });
        }
      })
      .addCase(updateGroupInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(batchAssignQuestionsToUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(batchAssignQuestionsToUser.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;

        // Update the assigned questions in the questionData array
        if (
          state.questionData &&
          action.payload &&
          action.payload.assignedQuestionIds.length > 0
        ) {
          state.questionData = state.questionData.map((question) => {
            const questionId = question.id || question.clientId;
            if (action.payload?.assignedQuestionIds.includes(questionId)) {
              return {
                ...question,
                assignedUserId: action.payload.userId,
                dueDate: action.payload.dueDate,
              };
            }
            return question;
          });
        }
      })
      .addCase(batchAssignQuestionsToUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(batchUnassignQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(batchUnassignQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        // Update the unassigned questions in the questionData array
        if (
          state.questionData &&
          action.payload &&
          action.payload.unassignedQuestionIds.length > 0
        ) {
          state.questionData = state.questionData.map((question) => {
            const questionId = question.id || question.clientId;
            if (action.payload?.unassignedQuestionIds.includes(questionId)) {
              return {
                ...question,
                assignedUserId: undefined,
                dueDate: undefined,
              };
            }
            return question;
          });
        }
      })
      .addCase(batchUnassignQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setQuestionData,
  clearQuestionData,
  handleChangeQuestion,
  setFilters,
  resetFilters,
} = questionDataManagerSlice.actions;
export default questionDataManagerSlice;
