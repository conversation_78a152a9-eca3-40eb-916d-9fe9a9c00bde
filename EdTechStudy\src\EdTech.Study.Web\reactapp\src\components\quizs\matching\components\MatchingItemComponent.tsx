import { CloseOutlined, CheckOutlined } from '@ant-design/icons';
import { Badge, Button, Tooltip } from 'antd';
import { memo } from 'react';

export interface MatchingItemProps {
  index: number;
  originalIndex: number;
  text: string;
  isSelected: boolean;
  isMatched: boolean;
  matchedItemLabels?: string[];
  onClick: () => void;
  onRemove?: (index: number) => void;
  side: 'left' | 'right';
  disabled?: boolean;
  showResult?: boolean;
  isCorrect?: boolean;
}

const MatchingItem: React.FC<MatchingItemProps> = ({
  index,
  originalIndex,
  text,
  isSelected,
  isMatched,
  matchedItemLabels,
  onClick,
  onRemove,
  side,
  disabled = false,
  showResult = false,
  isCorrect,
}) => {
  const isLeft = side === 'left';
  // Use display index for consistent labeling across modes
  const itemLabel = isLeft ? `${index + 1}` : String.fromCharCode(65 + index);

  let borderColorClass = '';
  let bgColorClass = '';

  if (showResult && isMatched) {
    // Chế độ xem kết quả - hiển thị màu theo đúng/sai
    if (isCorrect) {
      borderColorClass = 'tailwind-border-green-500';
      bgColorClass = 'matching-item-result-correct'; // #F6FFED
    } else {
      borderColorClass = 'tailwind-border-red-500';
      bgColorClass = 'matching-item-result-incorrect'; // #FFF1F0
    }
  } else if (isSelected) {
    // Chế độ làm bài - item được chọn
    borderColorClass = 'tailwind-border-primary';
    bgColorClass = 'tailwind-bg-selected';
  } else {
    // Chế độ làm bài - tất cả item khác đều có nền trắng
    borderColorClass = 'tailwind-border-gray-200';
    bgColorClass = 'tailwind-bg-white';
  }

  return (
    <div
      data-index={index}
      data-original-index={originalIndex}
      data-side={side}
      onClick={disabled ? undefined : onClick}
      className={`
        tailwind-p-3
        tailwind-rounded-lg
        tailwind-border-2
        tailwind-transition-all
        tailwind-flex
        tailwind-items-center
        tailwind-justify-between
        tailwind-mb-3
        ${borderColorClass}
        ${bgColorClass}
        ${
          !isSelected && !isMatched && !showResult
            ? 'hover:tailwind-bg-gray-50'
            : ''
        }
        ${
          disabled
            ? 'tailwind-cursor-not-allowed tailwind-opacity-70'
            : 'tailwind-cursor-pointer'
        }
      `}
    >
      <div className="tailwind-flex tailwind-items-center tailwind-gap-3 tailwind-flex-grow">
        <span className="tailwind-ml-1">
          {itemLabel}. {text}
        </span>
      </div>

      {/* Result Icons for showResult mode */}
      {showResult && isMatched && (
        <div className="tailwind-flex tailwind-items-center tailwind-ml-2">
          {isCorrect ? (
            <CheckOutlined className="tailwind-text-green-500 tailwind-text-lg" />
          ) : (
            <CloseOutlined className="tailwind-text-red-500 tailwind-text-lg" />
          )}
        </div>
      )}

      {/* Connection Management for non-result mode */}
      {!showResult &&
        isMatched &&
        onRemove &&
        matchedItemLabels &&
        matchedItemLabels.length > 0 && (
          <div className="tailwind-flex tailwind-items-center tailwind-gap-1 tailwind-ml-2">
            <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-1 tailwind-items-center tailwind-max-w-[150px]">
              {matchedItemLabels.map((label, idx) => (
                <Tooltip key={idx} title={`Xóa kết nối với ${label}`}>
                  <Button
                    type="default"
                    size="small"
                    className="!tailwind-p-0 !tailwind-w-10 tailwind-inline-flex tailwind-items-center tailwind-justify-center tailwind-bg-gray-500 tailwind-text-white tailwind-color-white tailwind-rounded-md hover:tailwind-text-gray-500 hover:tailwind-color-gray-500 hover:tailwind-border-gray-500"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemove(idx);
                    }}
                    disabled={disabled}
                  >
                    <span className="tailwind-text-xs tailwind-mr-0">
                      {label}
                    </span>
                    <CloseOutlined className="tailwind-text-xs" />
                  </Button>
                </Tooltip>
              ))}
            </div>
          </div>
        )}
    </div>
  );
};

export default memo(MatchingItem);
