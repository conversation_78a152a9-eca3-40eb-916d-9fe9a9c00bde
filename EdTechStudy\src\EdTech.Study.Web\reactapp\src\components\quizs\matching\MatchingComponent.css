/* MatchingComponent.css */

.matching-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.matching-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Matching Item Styles */
.matching-item-result-correct {
  background-color: #f6ffed !important;
  border-color: #52c41a !important;
}

.matching-item-result-incorrect {
  background-color: #fff1f0 !important;
  border-color: #ff4d4f !important;
}

/* Selection state styles */
.tailwind-bg-selected {
  background-color: var(--edtt-color-primary-100, #e6f4ff) !important;
}

/* Canvas styling - Enhanced for responsive behavior */
canvas {
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none !important;
  width: 100% !important;
  height: 100% !important;
}

/* Ensure container has proper positioning for canvas */
.tailwind-relative {
  position: relative;
}

/* Connection container enhanced positioning */
.matching-connection-container {
  position: relative;
  overflow: visible;
  min-height: 400px;
}

/* Enhanced z-index management for proper layering */
.tailwind-z-10 {
  z-index: 10;
}

.tailwind-z-20 {
  z-index: 20;
}

/* Ensure matching items are properly positioned for canvas calculations */
[data-side="left"], 
[data-side="right"] {
  position: relative;
  z-index: 20;
}

/* Connection summary animations */
.tailwind-bg-gray-50:hover {
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

/* Form styling for config mode */
.form-config .ant-form-item-label {
  font-weight: 500;
  color: var(--edtt-color-text-primary, #1f2937);
}

.form-config .ant-input[variant="underlined"] {
  border-bottom: 2px solid var(--edtt-color-border-default, #d1d5db);
  border-radius: 0;
  background: transparent;
}

.form-config .ant-input[variant="underlined"]:focus {
  border-bottom-color: var(--edtt-color-primary, #1890ff);
  box-shadow: 0 1px 0 0 var(--edtt-color-primary, #1890ff);
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .tailwind-grid-cols-2 {
    grid-template-columns: 1fr !important;
    gap: 2rem !important;
  }
  
  .tailwind-gap-10 {
    gap: 1rem !important;
  }
  
  .matching-question-preview {
    font-size: 14px;
  }
  
  /* Adjust canvas positioning for mobile */
  .matching-connection-container {
    min-height: 300px;
  }
  
  /* Ensure proper spacing on mobile */
  .tailwind-p-4 {
    padding: 1rem !important;
  }
}

@media (max-width: 480px) {
  .matching-connection-container {
    min-height: 250px;
  }
  
  /* Smaller gap for very small screens */
  .tailwind-grid-cols-2 {
    gap: 1rem !important;
  }
}

/* Dark theme support */
[data-theme="dark"] .matching-card {
  background-color: var(--edtt-color-bg-card, #1f1f1f);
  border-color: var(--edtt-color-border-default, #424242);
}

[data-theme="dark"] .tailwind-bg-gray-50 {
  background-color: var(--edtt-color-bg-elevated, #2a2a2a) !important;
}

[data-theme="dark"] .tailwind-text-gray-600 {
  color: var(--edtt-color-text-secondary, #a0a0a0) !important;
}

/* Animation for connections */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tailwind-bg-indigo-50,
.tailwind-bg-green-50 {
  animation: fadeIn 0.3s ease-out;
}

/* Enhanced hover effects for interactive elements */
.tailwind-cursor-pointer:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.tailwind-cursor-pointer:active {
  transform: translateY(0);
}

/* Smooth transition for resize events */
.matching-connection-container * {
  transition: all 0.2s ease-out;
}

/* Progress bar styling */
.tailwind-bg-indigo-500 {
  background: linear-gradient(90deg, var(--edtt-color-primary, #1890ff) 0%, var(--edtt-color-primary-600, #1565c0) 100%);
  transition: width 0.3s ease;
}

/* Button styling consistency */
.ant-btn-sm {
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 6px;
}

.ant-btn-sm .anticon {
  font-size: 12px;
}

/* Canvas high-DPI support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Ensure proper layout during resize transitions */
.matching-connection-container.resizing {
  overflow: hidden;
}

.matching-connection-container.resizing canvas {
  opacity: 0.8;
  transition: opacity 0.1s ease;
}

/* Loading state for canvas */
.canvas-loading {
  position: relative;
}

.canvas-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--edtt-color-border-default, #d1d5db);
  border-top-color: var(--edtt-color-primary, #1890ff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 15;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Enhance focus states for accessibility */
[data-side]:focus-within {
  outline: 2px solid var(--edtt-color-primary, #1890ff);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Smooth animations for connection state changes */
.matching-item-component {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.matching-item-component.selected {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.matching-item-component.matched {
  transform: scale(1.01);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}