// hooks/useResponsiveCanvas.ts
import { useEffect, useRef, useCallback, useState } from 'react';

export interface CanvasConnection {
  startElement: HTMLElement;
  endElement: HTMLElement;
  id: string;
  color?: string;
  animated?: boolean;
}

export interface UseResponsiveCanvasOptions {
  debounceMs?: number;
  enableHighDPI?: boolean;
  connectionStyle?: {
    lineWidth?: number;
    lineCap?: CanvasLineCap;
    lineJoin?: CanvasLineJoin;
    shadowBlur?: number;
    shadowColor?: string;
  };
}

export const useResponsiveCanvas = (
  options: UseResponsiveCanvasOptions = {}
) => {
  const {
    debounceMs = 16,
    enableHighDPI = true,
    connectionStyle = {
      lineWidth: 3,
      lineCap: 'round',
      lineJoin: 'round',
      shadowBlur: 4,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
    },
  } = options;

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const debounceTimeoutRef = useRef<number | null>(null);

  const [isReady, setIsReady] = useState(false);
  const [connections, setConnections] = useState<CanvasConnection[]>([]);

  // Setup canvas with high DPI support
  const setupCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const containerRect = container.getBoundingClientRect();
    const devicePixelRatio = enableHighDPI ? window.devicePixelRatio || 1 : 1;

    // Set actual canvas size
    canvas.width = containerRect.width * devicePixelRatio;
    canvas.height = containerRect.height * devicePixelRatio;

    // Set display size
    canvas.style.width = containerRect.width + 'px';
    canvas.style.height = containerRect.height + 'px';

    // Scale context for high DPI
    if (enableHighDPI) {
      ctx.scale(devicePixelRatio, devicePixelRatio);
    }

    // Apply connection styling
    ctx.lineWidth = connectionStyle.lineWidth || 3;
    ctx.lineCap = connectionStyle.lineCap || 'round';
    ctx.lineJoin = connectionStyle.lineJoin || 'round';

    setIsReady(true);
  }, [enableHighDPI, connectionStyle]);

  // Draw all connections
  const drawConnections = useCallback(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container || !isReady) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const containerRect = container.getBoundingClientRect();

    connections.forEach((connection) => {
      const {
        startElement,
        endElement,
        color = '#6366f1',
        animated = false,
      } = connection;

      if (!startElement || !endElement) return;

      const startRect = startElement.getBoundingClientRect();
      const endRect = endElement.getBoundingClientRect();

      // Calculate connection points
      const startX = startRect.right - containerRect.left;
      const startY = startRect.top + startRect.height / 2 - containerRect.top;
      const endX = endRect.left - containerRect.left;
      const endY = endRect.top + endRect.height / 2 - containerRect.top;

      // Validate coordinates
      const maxX =
        canvas.width / (enableHighDPI ? window.devicePixelRatio || 1 : 1);
      const maxY =
        canvas.height / (enableHighDPI ? window.devicePixelRatio || 1 : 1);

      if (
        startX < 0 ||
        startY < 0 ||
        endX < 0 ||
        endY < 0 ||
        startX > maxX ||
        endX > maxX ||
        startY > maxY ||
        endY > maxY
      ) {
        return;
      }

      // Setup shadow
      if (connectionStyle.shadowBlur && connectionStyle.shadowColor) {
        ctx.shadowColor = connectionStyle.shadowColor;
        ctx.shadowBlur = connectionStyle.shadowBlur;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
      }

      // Create gradient or use solid color
      let strokeStyle: string | CanvasGradient;
      if (color.includes(',')) {
        // Multiple colors - create gradient
        const colors = color.split(',').map((c) => c.trim());
        const gradient = ctx.createLinearGradient(startX, startY, endX, endY);
        colors.forEach((c, i) => {
          gradient.addColorStop(i / (colors.length - 1), c);
        });
        strokeStyle = gradient;
      } else {
        strokeStyle = color;
      }

      ctx.strokeStyle = strokeStyle;

      // Draw curved connection line
      ctx.beginPath();
      ctx.moveTo(startX, startY);

      // Calculate control points for smooth bezier curve
      const controlOffset = Math.abs(endX - startX) * 0.4;
      const controlPoint1X = startX + controlOffset;
      const controlPoint1Y = startY;
      const controlPoint2X = endX - controlOffset;
      const controlPoint2Y = endY;

      ctx.bezierCurveTo(
        controlPoint1X,
        controlPoint1Y,
        controlPoint2X,
        controlPoint2Y,
        endX,
        endY
      );
      ctx.stroke();

      // Draw connection points
      const startColor = Array.isArray(color)
        ? color[0]
        : color.includes(',')
        ? color.split(',')[0].trim()
        : color;
      const endColor = Array.isArray(color)
        ? color[1]
        : color.includes(',')
        ? color.split(',')[1]?.trim() || startColor
        : color;

      // Start point
      ctx.fillStyle = startColor;
      ctx.beginPath();
      ctx.arc(startX, startY, 6, 0, Math.PI * 2);
      ctx.fill();

      // End point
      ctx.fillStyle = endColor;
      ctx.beginPath();
      ctx.arc(endX, endY, 6, 0, Math.PI * 2);
      ctx.fill();

      // Reset shadow
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // Add animation effect if enabled
      if (animated) {
        const time = Date.now() * 0.005;
        const pulseRadius = 6 + Math.sin(time + connection.id.length) * 2;

        ctx.globalAlpha = 0.6;
        ctx.fillStyle = startColor;
        ctx.beginPath();
        ctx.arc(startX, startY, pulseRadius, 0, Math.PI * 2);
        ctx.fill();

        ctx.fillStyle = endColor;
        ctx.beginPath();
        ctx.arc(endX, endY, pulseRadius, 0, Math.PI * 2);
        ctx.fill();
        ctx.globalAlpha = 1;
      }
    });
  }, [connections, isReady, enableHighDPI, connectionStyle]);

  // Debounced draw function
  const debouncedDraw = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      animationFrameRef.current = requestAnimationFrame(drawConnections);
    }, debounceMs);
  }, [drawConnections, debounceMs]);

  // Setup resize observer
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    setupCanvas();

    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setupCanvas();
        debouncedDraw();
      }
    });

    resizeObserverRef.current.observe(container);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [setupCanvas, debouncedDraw]);

  // Window resize fallback
  useEffect(() => {
    const handleResize = () => {
      setupCanvas();
      debouncedDraw();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [setupCanvas, debouncedDraw]);

  // Redraw when connections change
  useEffect(() => {
    if (isReady) {
      debouncedDraw();
    }
  }, [connections, isReady, debouncedDraw]);

  // Public API
  const addConnection = useCallback((connection: CanvasConnection) => {
    setConnections((prev) => {
      const existing = prev.find((c) => c.id === connection.id);
      if (existing) {
        return prev.map((c) => (c.id === connection.id ? connection : c));
      }
      return [...prev, connection];
    });
  }, []);

  const removeConnection = useCallback((id: string) => {
    setConnections((prev) => prev.filter((c) => c.id !== id));
  }, []);

  const updateConnection = useCallback(
    (id: string, updates: Partial<CanvasConnection>) => {
      setConnections((prev) =>
        prev.map((c) => (c.id === id ? { ...c, ...updates } : c))
      );
    },
    []
  );

  const clearConnections = useCallback(() => {
    setConnections([]);
  }, []);

  const redraw = useCallback(() => {
    if (isReady) {
      drawConnections();
    }
  }, [isReady, drawConnections]);

  const forceRedraw = useCallback(() => {
    setupCanvas();
    if (isReady) {
      drawConnections();
    }
  }, [setupCanvas, isReady, drawConnections]);

  return {
    canvasRef,
    containerRef,
    isReady,
    connections,
    addConnection,
    removeConnection,
    updateConnection,
    clearConnections,
    redraw,
    forceRedraw,
  };
};

export default useResponsiveCanvas;
