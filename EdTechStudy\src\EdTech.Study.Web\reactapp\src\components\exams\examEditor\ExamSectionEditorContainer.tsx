import { connect } from 'react-redux';
import ExamSectionEditor from './ExamSectionEditor';
import { ExamSectionEditorStateToProps } from './ExamSectionEditor';

const mapStateToProps = (state: any): ExamSectionEditorStateToProps => {
  return {
    indexItems: state.examDataManager.indexItems,
  };
};
const ExamSectionEditorContainer = connect(mapStateToProps, null, null, {
  forwardRef: true,
})(ExamSectionEditor);

export default ExamSectionEditorContainer;
