﻿const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const fs = require('fs');
const webpack = require('webpack');

// Function to recursively delete directory contents
const cleanDirectory = (directory) => {
  if (fs.existsSync(directory)) {
    console.log(`Cleaning directory: ${directory}`);
    fs.rmSync(directory, { recursive: true, force: true });
    fs.mkdirSync(directory, { recursive: true });
    console.log(`Directory cleaned: ${directory}`);
  }
};

module.exports = (env) => {
  // Environment variables
  const envVars = {
    NODE_ENV: env.production ? 'production' : 'development',
  };
  // Common paths
  const paths = {
    src: path.resolve(__dirname, 'src'),
    public: path.resolve(__dirname, 'public'),
    output: env.production
      ? path.resolve(__dirname, '../wwwroot/EdTech/reactapp')
      : path.resolve(__dirname, 'dist'),
  };

  // Pages configuration - cập nhật theo vite.config.ts
  const pages = [
    {
      name: 'DemoLessonPage',
      entry: './src/pages/DemoLessonPage.tsx',
      template: 'public/demo.html',
      filename: 'demo.html',
    },
    {
      name: 'PreviewLessonPage',
      entry: './src/pages/PreviewLessonPage.tsx',
      template: 'public/preview.html',
      filename: 'preview.html',
    },
    {
      name: 'QuestionPage',
      entry: './src/pages/QuestionPage.tsx',
      template: 'public/question.html',
      filename: 'question.html',
    },
    {
      name: 'IconStorePage',
      entry: './src/pages/IconStorePage.tsx',
      template: 'public/iconStore.html',
      filename: 'iconStore.html',
    },
    {
      name: 'ExamManagementPage',
      entry: './src/pages/ExamManagementPage.tsx',
      template: 'public/exams.html',
      filename: 'exams.html',
    },
    {
      name: 'BasePage',
      entry: './src/pages/BasePage.tsx',
      template: 'public/index.html',
      filename: 'index.html',
    },
    {
      name: 'PracticeExamPage',
      entry: './src/pages/PracticeExamPage.tsx',
      template: 'public/practiceExams.html',
      filename: 'practiceExams.html',
    },
  ];

  // Generate entries and HTML plugins from pages configuration
  const { entries, htmlPlugins } = pages.reduce(
    (acc, page) => ({
      entries: { ...acc.entries, [page.name]: page.entry },
      htmlPlugins: [
        ...acc.htmlPlugins,
        new HtmlWebpackPlugin({
          title: 'index',
          template: env.production ? 'public/index.html' : page.template,
          filename: page.filename,
          chunks: [page.name],
          templateParameters: {
            idRoot: page.name,
          },
        }),
      ],
    }),
    { entries: {}, htmlPlugins: [] }
  );

  // Module rules configuration
  const moduleRules = [
    {
      test: /\.(js|jsx)$/,
      exclude: /node_modules/,
      use: ['babel-loader'],
    },
    {
      test: /\.(ts|tsx)$/,
      exclude: /node_modules/,
      use: [
        {
          loader: 'ts-loader',
          options: {
            // Bỏ qua lỗi TypeScript khi build
            transpileOnly: true,
            // Tắt kiểm tra lỗi
            compilerOptions: {
              noUnusedLocals: false,
              noUnusedParameters: false,
            },
          },
        },
      ],
    },
    {
      test: /\.(css|scss)$/,
      use: [MiniCssExtractPlugin.loader, 'css-loader', 'postcss-loader'],
    },
    {
      test: /\.(jpg|jpeg|png|gif|svg)$/,
      type: 'asset/resource',
      generator: {
        filename: 'assets/images/[name][ext]',
      },
    },
    {
      test: /\.(mp3)$/,
      use: ['file-loader'],
    },
  ];

  // Resolve configuration
  const resolveConfig = {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      // Thêm alias cho process/browser
      'process/browser': require.resolve('process/browser.js'),
    },
    fallback: {
      fs: false,
      buffer: require.resolve('buffer/'),
      stream: require.resolve('stream-browserify'),
      util: require.resolve('util/'),
      process: require.resolve('process/browser.js'),
    },
  };

  // Output configuration
  const outputConfig = {
    path: paths.output,
    filename: '[name].js',
    chunkFilename: '[name].js?_v=[chunkhash]',
    publicPath: env.production ? '/EdTech/reactapp/' : '/',
  };

  // DevServer configuration
  const devServerConfig = {
    static: [
      {
        directory: paths.public,
        publicPath: '/',
      },
      {
        directory: paths.output,
        publicPath: '/',
      },
    ],
    port: 8080,
    open: '/demo.html',
    historyApiFallback: {
      index: '/index.html',
      // Optional: You can specify custom rewrites if needed
      rewrites: [
        { from: /^\/ExamManagement/, to: '/exams.html' },
        { from: /./, to: '/index.html' },
      ],
    },
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
      logging: 'error',
    },
  };

  // Clean output directories before build
  if (env.production) {
    const prodOutputDir = path.resolve(__dirname, '../wwwroot/EdTech/reactapp');
    cleanDirectory(prodOutputDir);
  } else {
    const devOutputDir = path.resolve(__dirname, 'dist');
    cleanDirectory(devOutputDir);
  }

  // Plugins configuration
  const plugins = [
    ...htmlPlugins,
    new NodePolyfillPlugin(),
    // CSS extraction plugin - outputs two CSS files
    new MiniCssExtractPlugin({
      filename: env.production ? 'assets/css/[name].css' : '[name].css',
      chunkFilename: env.production
        ? 'assets/css/[name].css'
        : '[name].chunk.css',
    }),
    // Thêm polyfill cho Buffer và process
    new webpack.ProvidePlugin({
      Buffer: ['buffer', 'Buffer'],
      process: 'process/browser.js',
    }),
    // Thêm DefinePlugin để cung cấp process.env
    new webpack.DefinePlugin({
      'process.env': JSON.stringify(process.env),
    }),
    new webpack.DefinePlugin({
      'import.meta.env.MODE': JSON.stringify(process.env),
      'import.meta.env.DEV': JSON.stringify(!env.production),
      'import.meta.env.PROD': JSON.stringify(env.production),
    }),
  ];

  return {
    mode: envVars.NODE_ENV,
    entry: entries,
    module: {
      rules: moduleRules,
    },
    resolve: resolveConfig,
    plugins,
    devtool: 'source-map',
    devServer: devServerConfig,
    output: outputConfig,
    optimization: {
      runtimeChunk: 'single',
      splitChunks: {
        cacheGroups: {
          // Extract vendor CSS into a separate file
          vendorStyles: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/].*\.(css|scss)$/,
            chunks: 'all',
            enforce: true,
            priority: 20,
          },
          // Extract application CSS into a separate file
          appStyles: {
            name: 'app',
            test: /(?<!node_modules)[\\/].*\.(css|scss)$/,
            chunks: 'all',
            enforce: true,
            priority: 10,
          },
        },
      },
    },
    // Bỏ qua các cảnh báo khi build và khi chạy dev server
    ignoreWarnings: [
      /export .* was not found in/,
      /module .* should be in the dependencies/,
      /.*export 'default'.*was not found.*/,
      /\[mini-css-extract-plugin\]/,
      /Conflicting order/,
      /size limit/,
      /entrypoint size limit/,
      /webpack performance recommendations/,
      /asset size limit/,
      /The following asset\(s\) exceed the recommended size limit/,
      /You can limit the size of your bundles by using import\(\)/,
    ],
    // Cấu hình stats - chỉ hiển thị lỗi, bỏ qua cảnh báo
    stats: {
      errorDetails: true,
      warnings: false,
      colors: true,
      assets: false,
      modules: false,
      performance: false,
    },
  };
};
