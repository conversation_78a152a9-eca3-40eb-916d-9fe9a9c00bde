import {
  forwardRef,
  Ref,
  useCallback,
  useImperative<PERSON>andle,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ExamGroupQuestion,
  ExamQuestion,
  ExamSection,
} from '../../../interfaces/exams/examBase';
import { createNewSection } from '../../../utils/examUtils';
import {
  Button,
  Card,
  Collapse,
  Empty,
  Form,
  Input,
  Space,
  Typography,
} from 'antd';
import {
  DeleteFilled,
  EditFilled,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import ReactQuill from 'react-quill';
import { examLocalization } from '../examLocalization';
import QuestionEditor from '../../quizs/questionEditor/QuestionEditor';
import ModalSelectQuestionType from '../../quizs/practiceEngines/ModalSelectQuestionType';
import practiceLocalization from '../../quizs/localization';
import { QuestionTemplateFactory } from '../../quizs/practiceEngines/questionTemplates';
import QuestionBankModal from '../../quizs/questionBank/QuestionBankModal';
import { QuestionDto } from '../../quizs/questionBank/questionBankTemplate';
import { forEach } from 'lodash';
import { ExamStatus } from '../../../interfaces/exams/examEnums';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';
import { GroupQuestionEditor } from '../../quizs/questionEditor/GroupQuestionEditor';
import LoadingScreen from '../../common/Loading/LoadingScreen';
import React from 'react';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import { NestedItem } from '../../../store/slices/ExamSlices/examDataManagerSlice';

export interface ExamSectionEditorProps extends ExamSectionEditorStateToProps {
  section: ExamSection | null; // NULL for creating
  questionOrGroupIndex?: number;
  subGroupItemIndex?: number;

  index: number;

  handleCreateQuestion: (question: BaseQuestion, position?: number) => any;
  handleUpdateSection: (
    clientId: string,
    updateSection: Partial<ExamSection>
  ) => any;
  handleDelete: () => any;
  handleTitleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;

  handleMoveQuestion?: (position: {
    sectionIndex: number;
    questionOrGroupIndex: number;
    subIndex?: number;
  }) => any;
}

export interface ExamSectionEditorStateToProps {
  indexItems: NestedItem[];
}

export interface ExamSectionEditorRef {
  openModalCreateQuestion: () => void;
}

const { Title, Text } = Typography;

const ExamSectionEditor = forwardRef<
  ExamSectionEditorRef,
  ExamSectionEditorProps
>((props: ExamSectionEditorProps, ref: Ref<ExamSectionEditorRef>) => {
  const {
    section,
    handleCreateQuestion,
    handleUpdateSection,
    handleDelete,
    handleTitleChange,
    handleMoveQuestion,

    index: sectionIndex,
    questionOrGroupIndex = 0,
    subGroupItemIndex,
    indexItems,
  } = props;
  const { subjects, lessonGrades: grades } = useQuestionContext();
  const [modalVisible, setModalVisible] = useState(false);
  const [modalQuestionBankVisible, setQuestionBankModalVisible] =
    useState(false);
  const sectionContentRef = useRef<HTMLDivElement>(null);

  const editedSection = useMemo(() => {
    return section ?? createNewSection();
  }, [section]);

  const maxHeightCache = useRef<number>(0);
  const sectionContentWrapperRef = useRef<HTMLDivElement>(null);
  const sectionSubItemComposer = useMemo(() => {
    const res: (BaseQuestion | ExamGroupQuestion)[] = [];
    const orderedSectionIndex = indexItems.findIndex(
      (item) => item.currentId === editedSection.clientId
    );
    const orderedSectionChild =
      orderedSectionIndex >= 0 ? indexItems[orderedSectionIndex].children : [];
    const orderedSectionChildMap = new Map(
      orderedSectionChild.map((item, index) => [item.currentId, index])
    );

    if (editedSection.questions) {
      const questions = editedSection.questions.map((q) => {
        return {
          ...q,
          nodeType: 'question',
        };
      });
      res.push(...questions);
    }
    if (editedSection.groupQuestions) {
      const groupQuestions = editedSection.groupQuestions.map((q) => {
        return {
          ...q,
          nodeType: 'group',
        } as ExamGroupQuestion;
      });

      res.push(...groupQuestions);
    }
    return res.sort((a, b) => {
      const aIndex = orderedSectionChildMap.get(a.clientId);
      const bIndex = orderedSectionChildMap.get(b.clientId);
      return (aIndex ?? 0) - (bIndex ?? 0);
    });
  }, [editedSection.questions, editedSection.groupQuestions, indexItems]);

  const handleUpdateQuestion = useCallback(
    (updatedQuestion: BaseQuestion) => {
      handleUpdateSection(editedSection.clientId, {
        questions: editedSection.questions.map((q, index) =>
          index === questionOrGroupIndex ? updatedQuestion : q
        ) as ExamQuestion[],
      });
    },
    [editedSection.clientId, editedSection.questions, questionOrGroupIndex]
  );

  const handleAddQuestion = (type: string, position?: number) => {
    if (type == practiceLocalization.questionbank) {
      setQuestionBankModalVisible(true);
      return;
    }
    const factory = QuestionTemplateFactory.getInstance();
    const newQuestion = factory.create(type);
    if (!newQuestion) {
      return;
    }
    newQuestion.statusEntity = ExamStatus.Draft;
    if (handleCreateQuestion) {
      handleCreateQuestion(newQuestion, position);
      if (position)
        handleMoveQuestion?.({
          sectionIndex: sectionIndex,
          questionOrGroupIndex:
            typeof subGroupItemIndex === 'number'
              ? questionOrGroupIndex
              : position,
          subIndex:
            typeof subGroupItemIndex === 'number' ? position : undefined,
        });
    }
    // Close modal
    setModalVisible(false);
  };

  const handleSelectQuestions = (
    selectedQuestions: QuestionDto[],
    position?: number
  ) => {
    setModalVisible(false);
    setQuestionBankModalVisible(false);
    // Do something with the selected questions
    let currentPosition = position;

    forEach(selectedQuestions, (questionNew, index) => {
      const factory = QuestionTemplateFactory.getInstance();
      const newQuestion = factory.createWithProps(
        practiceLocalization.questionbank,
        {
          ...questionNew,
          SyncQuestion: true,
          LastSyncQuestionId: questionNew.Id,
          statusEntity: questionNew.Status,
          nodeType: 'question',
        }
      );
      if (!newQuestion) {
        return;
      }

      if (handleCreateQuestion) {
        handleCreateQuestion(
          newQuestion,
          !currentPosition ? currentPosition : currentPosition + index
        );
        if (!currentPosition) {
          currentPosition = 0;
        }
        setTimeout(() => {
          handleMoveQuestion?.({
            sectionIndex: sectionIndex,
            questionOrGroupIndex: Math.min(
              typeof currentPosition === 'number' ? currentPosition : 0,
              editedSection.questions.length
            ),
            subIndex: subGroupItemIndex,
          });
        }, 500);
      }
    });
  };

  const collapseTitle = [
    {
      key: '1',
      label: (
        <h3 className="tailwind-m-0 tailwind-text-lg">
          {editedSection.title}
          <EditFilled className="tailwind-ml-2 tailwind-text-primary" />
        </h3>
      ),
      children: (
        <div>
          <div
            id={editedSection.clientId}
            className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2 tailwind-p-2 tailwind-bg-primary tailwind-text-white tailwind-mx-0"
          >
            <div className="tailwind-flex tailwind-grow tailwind-items-center">
              <Form.Item
                label={
                  <span className="tailwind-text-white">Tiêu đề phần thi</span>
                }
                className="tailwind-w-full tailwind-mb-0"
                required
              >
                <Input
                  className="tailwind-text-xl tailwind-font-bold tailwind-text-white"
                  variant="underlined"
                  style={{
                    background: 'transparent',
                  }}
                  defaultValue={editedSection.title}
                  placeholder="Thêm tiêu đề phần thi"
                  onChange={handleTitleChange}
                />
              </Form.Item>
            </div>
            <div className="tailwind-h-full tailwind-flex tailwind-items-center">
              <Button
                className="tailwind-bg-white tailwind-text-red"
                size="large"
                type="text"
                danger
                icon={<DeleteFilled />}
                onClick={handleDelete}
              />
            </div>
          </div>
          <div>
            <label style={{ textTransform: 'capitalize' }}>
              {examLocalization.instructions}
            </label>
            <ReactQuill
              defaultValue={editedSection.instructions || ''}
              onChange={(value) => {
                handleUpdateSection(editedSection.clientId, {
                  instructions: value,
                });
              }}
            />
          </div>
        </div>
      ),
    },
  ];

  useImperativeHandle(
    ref,
    () => ({
      openModalCreateQuestion: () => {
        setModalVisible(true);
      },
    }),
    [setModalVisible]
  );

  useLayoutEffect(() => {
    if (sectionContentWrapperRef.current) {
      const rect = sectionContentWrapperRef.current.getBoundingClientRect();
      if (rect.height > maxHeightCache.current) {
        maxHeightCache.current = rect.height;
      } else {
        sectionContentWrapperRef.current.style.minHeight =
          maxHeightCache.current + 'px';
      }
    }
  }, [questionOrGroupIndex]);

  return (
    <div
      ref={sectionContentWrapperRef}
      className="tailwind-max-h-[calc(100vh-200px)] tailwind-overflow-y-auto"
    >
      <Collapse items={collapseTitle} defaultActiveKey={[]} ghost></Collapse>
      <Card
        className="tailwind-mb-4 tailwind-shadow-sm exam-section-card tailwind-border-none"
        size="small"
        styles={{
          body: {
            borderColor: 'transparent',
          },
          cover: {
            borderColor: 'transparent',
          },
        }}
      >
        <div
          ref={sectionContentRef}
          id={'sectionContent-' + editedSection.clientId}
          className="tailwind-w-full tailwind-min-h-[50vh]"
        >
          <div>
            <Form.Item>
              <div className="tailwind-items-center tailwind-flex tailwind-gap-1">
                <div className="tailwind-grow section-question-editor-wrapper">
                  {sectionSubItemComposer.length > 0 ? (
                    sectionSubItemComposer[questionOrGroupIndex].nodeType ==
                    'question' ? (
                      <React.Suspense
                        fallback={
                          <div className="tailwind-h-[400px] tailwind-flex tailwind-items-center tailwind-justify-center">
                            <LoadingScreen />
                          </div>
                        }
                      >
                        <QuestionEditor
                          visible={true}
                          currentPosition={questionOrGroupIndex}
                          totalQuestion={sectionSubItemComposer.length}
                          question={
                            sectionSubItemComposer[
                              questionOrGroupIndex
                            ] as BaseQuestion
                          }
                          subjects={subjects ?? []}
                          grades={grades ?? []}
                          onCancel={() => {}}
                          onSave={handleUpdateQuestion}
                          onNext={() =>
                            handleMoveQuestion?.({
                              sectionIndex: sectionIndex,
                              questionOrGroupIndex: questionOrGroupIndex + 1,
                              subIndex: undefined,
                            })
                          }
                          onPrev={() =>
                            handleMoveQuestion?.({
                              sectionIndex: sectionIndex,
                              questionOrGroupIndex: questionOrGroupIndex - 1,
                              subIndex: undefined,
                            })
                          }
                        />
                      </React.Suspense>
                    ) : (
                      <GroupQuestionEditor
                        groupQuestion={
                          sectionSubItemComposer[
                            questionOrGroupIndex
                          ] as ExamGroupQuestion
                        }
                        questionIndex={subGroupItemIndex}
                      />
                    )
                  ) : (
                    <div className="tailwind-h-full tailwind-flex">
                      <div className="tailwind-text-center tailwind-m-auto">
                        <Empty
                          image={
                            <QuestionCircleOutlined className="tailwind-text-blue-500 tailwind-text-7xl" />
                          }
                          description={null}
                        />

                        <Title
                          level={4}
                          className="tailwind-text-center tailwind-mb-1"
                        >
                          Chưa có câu hỏi
                        </Title>
                        <Text className="tailwind-text-gray-500 tailwind-text-center mb-6">
                          Bắt đầu bằng việc thêm 1 câu hỏi mới
                        </Text>
                        <br />

                        <Space
                          direction="vertical"
                          size="middle"
                          className="tailwind-w-full tailwind-max-w-md tailwind-mt-5"
                        >
                          <Button
                            type="primary"
                            size="large"
                            onClick={() => setModalVisible(true)}
                            icon={<PlusOutlined />}
                            className="tailwind-w-full tailwind-bg-blue-500 tailwind-hover:tailwind-bg-blue-600 tailwind-h-6 tailwind-flex tailwind-items-center tailwind-justify-center"
                          >
                            Thêm câu hỏi
                          </Button>
                        </Space>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Form.Item>
          </div>
        </div>
      </Card>
      <ModalSelectQuestionType
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
        }}
        onSelectType={(type) => handleAddQuestion(type, questionOrGroupIndex)}
      />
      <QuestionBankModal
        visible={modalQuestionBankVisible}
        onCancel={() => setQuestionBankModalVisible(false)}
        onSelect={(q) => handleSelectQuestions(q, questionOrGroupIndex)}
      />
    </div>
  );
});

export default ExamSectionEditor;
