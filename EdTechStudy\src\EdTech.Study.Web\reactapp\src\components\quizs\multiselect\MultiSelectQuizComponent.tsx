import React, {
  memo,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
} from 'react';
import {
  Card,
  Checkbox,
  Button,
  Space,
  message,
  Input,
  Form,
  PopconfirmProps,
  Collapse,
  Row,
  Col,
} from 'antd';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  PlusOutlined,
  DeleteFilled,
  MenuOutlined,
} from '@ant-design/icons';
import {
  BaseQuestion,
  PracticeEngineContext,
} from '../../../interfaces/quizs/questionBase';
import '../quiz/QuizAnimations.css';
import {
  MultiSelectAnswer,
  MultiSelectQuestion,
  MultiSelectQuizComponentProps,
} from '../../../interfaces/quizs/multiSelectQuiz.interface';
import './MultiSelectStyles.css';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import practiceLocalization, { quizLocalization } from '../localization';
import 'react-quill/dist/quill.snow.css';
import { Guid } from 'guid-typescript';
import { standardQuestionRichTextEditorItems } from '../../../constants/edTechComponents/practices';
import { MathContent } from '../../common/MathJax/MathJaxWrapper';
import { ContentFormatType } from '../../../interfaces/exams/examEnums';
import BaseQuestionPreviewComponent from '../base/BaseQuestionPreviewComponent';
import ErrorBoundary from 'antd/es/alert/ErrorBoundary';
import useDebouncedCallback from '../../../hooks/apps/useDebouncedCallback';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { multiSelectCheckCorrectAnswer } from './multiSelectResultUtils';
import CustomRichText from '../../common/elements/Text/RichTextEditorComponent/v0/CustomRichText';

// Không cần Text nữa vì đã sử dụng BaseQuestionPreviewComponent
const { TextArea } = Input;

// Sortable Option Item Component for drag and drop
interface SortableMultiSelectOptionItemProps {
  option: any;
  index: number;
  field: any;
  onDelete: (id: string) => void;
  disabled: boolean;
  minOptionsCount: number;
}

const SortableMultiSelectOptionItem: React.FC<
  SortableMultiSelectOptionItemProps
> = ({ option, index, field, onDelete, disabled, minOptionsCount }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: option.clientId });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: isDragging ? '#f6ffed' : 'transparent',
    borderColor: isDragging ? '#b7eb8f' : 'transparent',
    display: 'flex',
    alignItems: 'center',
    marginBottom: '10px',
    padding: '8px',
    border: '1px solid transparent',
    borderRadius: '6px',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="multi-select-option-item tailwind-shadow-md"
    >
      <div
        {...attributes}
        {...listeners}
        className="tailwind-cursor-grab tailwind-text-gray-400 hover:tailwind-text-gray-700"
        style={{ padding: '4px', marginRight: '8px' }}
      >
        <MenuOutlined />
      </div>

      <Form.Item
        {...field}
        name={[field.name, 'isCorrect']}
        valuePropName="checked"
        noStyle
        className="multi-select-option-checkbox"
      >
        <Checkbox style={{ transform: 'scale(1.5)' }} />
      </Form.Item>

      <div className="tailwind-flex tailwind-items-center tailwind-w-full tailwind-ml-3">
        <span className="option-label" style={{ marginRight: '8px' }}>
          {String.fromCharCode(65 + index)}.
        </span>
        <Form.Item
          {...field}
          name={[field.name, 'content']}
          rules={[
            {
              required: true,
              message: 'Nội dung không được để trống!',
            },
          ]}
          noStyle
          className="multi-select-option-input tailwind-flex"
        >
          <Input
            size="middle"
            variant="underlined"
            style={{ flex: 1 }}
            placeholder={`Tùy chọn ${index + 1}`}
          />
        </Form.Item>
      </div>

      <Button
        type="text"
        danger
        icon={<DeleteOutlined />}
        className="multi-select-option-delete"
        onClick={() => onDelete(option.clientId)}
        disabled={disabled || minOptionsCount <= 2}
      />
    </div>
  );
};

const MultiSelectQuizComponent: React.FC<MultiSelectQuizComponentProps> = ({
  question,
  questionIndex,
  onComplete,
  allowManyTimes: _allowManyTimes = false,
  configMode = false,
  disabled = false,
  options = {
    hideDeleteButton: false,
    hideSaveButton: false,
    hideFeedback: false,
  },
  // Thêm props cho chế độ hiển thị kết quả
  showResult = false,
  isMarked = false,
  onToggleMark,
}) => {
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [form] = Form.useForm();

  // State for sortable options in config mode
  const [sortableOptions, setSortableOptions] = useState<any[]>(() => {
    return (
      question.options?.map((opt) => ({
        ...opt,
        key: opt.clientId,
      })) || []
    );
  });

  const { handleDeleteQuestion: externalHandleDeleteQuestion } = useContext(
    PracticeEngineContext
  );

  const { handleUpdateQuestion } = useUpdateQuestion();

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sortableOptions.findIndex(
        (option) => option.clientId === active.id
      );
      const newIndex = sortableOptions.findIndex(
        (option) => option.clientId === over.id
      );

      if (oldIndex !== -1 && newIndex !== -1) {
        const newOptions = arrayMove(sortableOptions, oldIndex, newIndex);

        // Update order property
        const updatedOptions = newOptions.map((opt, index) => ({
          ...opt,
          order: index,
        }));

        setSortableOptions(updatedOptions);

        // Update form
        form.setFieldsValue({
          options: updatedOptions,
        });

        // Update question
        const updatedQuestion: MultiSelectQuestion = {
          ...question,
          options: updatedOptions,
        };
        handleUpdateQuestion(updatedQuestion as BaseQuestion);
      }
    }
  };

  const isAnswered = useMemo(() => {
    return selectedAnswers.length > 0;
  }, [selectedAnswers]);

  const isCorrect = useMemo(() => {
    if (!showResult || !isAnswered) return false;
    const userSelectedAnswers =
      question.options?.filter((option) =>
        selectedAnswers.includes(option.clientId)
      ) || [];
    const checkResult = multiSelectCheckCorrectAnswer(
      question,
      userSelectedAnswers
    );
    return checkResult === true;
  }, [showResult, isAnswered, question, selectedAnswers]);

  // Add debounced update function
  const debouncedUpdateQuestion = useDebouncedCallback(
    (values: any) => {
      const updatedQuestion: MultiSelectQuestion = {
        ...question,
        title: values.title,
        content: values.content,
        description: values.content,
        points: values.points,
        explanation: values.explanation,
        options:
          values.options?.map((opt: any, index: number) => ({
            ...opt,
            order: index,
            clientId: opt.clientId || opt.key,
          })) || question.options,
      };
      handleUpdateQuestion(updatedQuestion as BaseQuestion);
    },
    [question, handleUpdateQuestion]
  );

  // Add form change handler
  const handleFormChange = useCallback(
    (_changedValues: any, allValues: any) => {
      console.log('_changedValues', _changedValues);
      if (configMode) {
        debouncedUpdateQuestion(allValues);
      }
    },
    [configMode, debouncedUpdateQuestion]
  );

  const handleAnswerSelect = (answerId: string) => {
    if (!isSubmitted && !disabled) {
      const newSelectedAnswers = selectedAnswers.includes(answerId)
        ? selectedAnswers.filter((id) => id !== answerId)
        : [...selectedAnswers, answerId];
      setSelectedAnswers(newSelectedAnswers);

      let answers = question.options.filter((que) =>
        newSelectedAnswers.includes(que.clientId)
      );

      form.setFieldValue(
        'options',
        answers.map((opt) => ({
          ...opt,
          key: opt.clientId,
        }))
      );
      // Store answers without auto-submitting
      if (onComplete) {
        onComplete(question.clientId, answers);
      }
    }
  };

  // Reset the question
  const handleReset = () => {
    // Reset selected answers from userSelect if available
    if (question.userSelect && Array.isArray(question.userSelect)) {
      const userSelectedIds = question.userSelect.map(
        (answer) => answer.clientId
      );
      setSelectedAnswers(userSelectedIds);
    } else {
      setSelectedAnswers([]);
    }
    setIsSubmitted(false);
  };

  // Initialize selected answers from userSelect when component mounts or question changes
  useEffect(() => {
    handleReset();
  }, [question.clientId, question.userSelect]);

  // Config mode functions
  const handleSaveConfig = async () => {
    try {
      const values = await form.validateFields();

      const updatedQuestion: MultiSelectQuestion = {
        ...question,
        title: values.title,
        content: values.content,
        description: values.content,
        points: values.points,
        explanation: values.explanation,
        options: values.options.map((opt: any, index: number) => ({
          ...opt,
          order: index,
          clientId: opt.clientId || opt.key,
        })),
      };

      handleUpdateQuestion(updatedQuestion as BaseQuestion);
      message.success('Lưu cấu hình thành công!');
    } catch (error) {
      // Form validation failed
      console.error('Validation failed:', error);
    }
  };

  const addNewOption = () => {
    const options = form.getFieldValue('options') || [];
    const newId = Guid.create().toString();
    const newOption = {
      key: newId,
      clientId: newId,
      content: 'Tùy chọn mới',
      contentFormat: ContentFormatType.Html,
      isCorrect: false,
      order: options.length,
    };

    const updatedOptions = [...options, newOption];

    form.setFieldsValue({
      options: updatedOptions,
    });

    setSortableOptions(updatedOptions);

    handleUpdateQuestion({
      options: updatedOptions,
      clientId: question.clientId,
    });
  };

  const removeOption = (id: string) => {
    const options = form.getFieldValue('options') || [];
    if (options.length <= 2) {
      message.warning('Phải có ít nhất 2 tùy chọn!');
      return;
    }

    const updatedOptions = options.filter(
      (opt: any) => opt.clientId !== id && opt.key !== id
    );

    form.setFieldsValue({
      options: updatedOptions,
    });

    setSortableOptions(updatedOptions);

    handleUpdateQuestion({
      clientId: question.clientId,
      options: updatedOptions,
    });
  };

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.clientId);
  };

  // Initialize form with question data
  useEffect(() => {
    if (configMode) {
      const formOptions =
        question.options?.map((opt) => ({
          ...opt,
          key: opt.clientId,
        })) || [];

      form.setFieldsValue({
        title: question.title,
        content: question.content,
        points: question.points || 1,
        explanation: question.explanation || '',
        options: formOptions,
      });

      setSortableOptions(formOptions);
    }
  }, [question, configMode, form]);

  // Reset state when user changes question
  useEffect(() => {
    handleReset();
  }, [question.clientId]);

  // Render config mode
  if (configMode) {
    return (
      <Card
        key={`${question.clientId}-config-mode`}
        className="quiz-card"
        extra={
          !options.hideSaveButton && (
            <>
              {!options.hideSaveButton && (
                <Button
                  type="primary"
                  onClick={handleSaveConfig}
                  className="flex-1 bg-blue-500 hover:bg-blue-600"
                  icon={<CheckOutlined />}
                >
                  Lưu cấu hình
                </Button>
              )}
            </>
          )
        }
      >
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          initialValues={{
            title: question.title,
            content: question.content,
            points: question.points || 1,
            explanation: question.explanation || '',
            options:
              question.options
                ?.slice()
                .sort((a, b) => a.order - b.order)
                .map((opt) => ({
                  ...opt,
                  key: opt.clientId,
                })) || [],
          }}
        >
          <Form.Item
            name="title"
            label="Tiêu đề câu hỏi"
            rules={[
              { required: true, message: 'Tiêu đề không được để trống!' },
            ]}
            hidden
          >
            <Input
              placeholder="Nhập tiêu đề câu hỏi"
              variant="underlined"
              className="tailwind-font-medium tailwind-text-lg"
            />
          </Form.Item>

          <Form.Item name="content" label="Nội dung" required>
            <ErrorBoundary>
              <CustomRichText
                height={200}
                value={question.content}
                onChangeValue={(_name, value) => {
                  handleFormChange(
                    {
                      content: value,
                    },
                    {
                      ...question,
                      content: value,
                    }
                  );
                }}
                toolbarSettingItems={[
                  'Bold',
                  'Italic',
                  'Underline',
                  'OrderedList',
                  'UnorderedList',
                  'Image',
                  'CreateLink',
                ]}
              />
            </ErrorBoundary>
          </Form.Item>

          <Form.Item
            name="points"
            label="Điểm"
            rules={[{ required: true, message: 'Vui lòng nhập điểm!' }]}
          >
            <Input type="number" min={1} style={{ width: '100px' }} />
          </Form.Item>

          <Form.Item
            label="Các tùy chọn đáp án (chọn đáp án đúng)"
            required
            rules={[
              {
                validator: async (_, options) => {
                  if (!options || options.length < 2) {
                    throw new Error('Phải có ít nhất 2 tùy chọn đáp án!');
                  }
                  if (!options.some((opt: any) => opt.isCorrect)) {
                    throw new Error('Phải chọn ít nhất 1 đáp án đúng!');
                  }
                  if (options.some((opt: any) => !opt.content?.trim())) {
                    throw new Error('Tất cả các tùy chọn phải có nội dung!');
                  }
                },
              },
            ]}
          >
            <Form.List name="options">
              {(fields, { add: _add, remove }) => (
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={sortableOptions.map((option) => option.clientId)}
                    strategy={verticalListSortingStrategy}
                  >
                    <Space
                      direction="vertical"
                      style={{ width: '100%' }}
                      className="multi-select-options-list"
                    >
                      {fields.map((field, index) => {
                        const option = form.getFieldValue([
                          'options',
                          field.name,
                        ]);
                        return (
                          <SortableMultiSelectOptionItem
                            key={field.key}
                            option={option}
                            index={index}
                            field={field}
                            onDelete={(id) => {
                              remove(field.name);
                              if (id) {
                                removeOption(id);
                              }
                            }}
                            disabled={disabled}
                            minOptionsCount={fields.length}
                          />
                        );
                      })}
                      <Button
                        type="dashed"
                        onClick={addNewOption}
                        icon={<PlusOutlined />}
                        style={{ width: '100%' }}
                        className="multi-select-add-option"
                      >
                        Thêm tùy chọn
                      </Button>
                    </Space>
                  </SortableContext>
                </DndContext>
              )}
            </Form.List>
          </Form.Item>

          <Form.Item
            name="explanation"
            label="Giải thích đáp án"
            rules={[{ required: false }]}
          >
            <TextArea rows={3} placeholder="Nhập giải thích cho đáp án đúng" />
          </Form.Item>

          {(!options.hideSaveButton || !options.hideDeleteButton) && (
            <Space>
              {!options.hideSaveButton && (
                <Button type="primary" onClick={handleSaveConfig}>
                  {quizLocalization.buttons.saveChanges}
                </Button>
              )}
              {!options.hideDeleteButton && (
                <PopconfirmAntdCustom
                  title={quizLocalization.buttons.deleteQuestion.confirmTitle}
                  onConfirm={deleteConfirm}
                  onCancel={() => {}}
                  okText={quizLocalization.buttons.deleteQuestion.yes}
                  cancelText={quizLocalization.buttons.deleteQuestion.no}
                >
                  <Button danger icon={<DeleteFilled />}>
                    {quizLocalization.buttons.deleteQuestion.button}
                  </Button>
                </PopconfirmAntdCustom>
              )}
            </Space>
          )}
        </Form>
      </Card>
    );
  }

  // Render normal mode
  const renderContent = () => {
    return (
      <div className="multi-select-question-preview">
        <MathContent html={question.content}></MathContent>
      </div>
    );
  };

  // Render phần tương tác (các lựa chọn)
  const renderInteraction = () => {
    return (
      <div className="multi-select-options">
        <Row gutter={[16, 16]} className="tailwind-w-full">
          {question.options
            .slice()
            .sort((a, b) => a.order - b.order)
            ?.map((option: MultiSelectAnswer) => {
              const isSelected = selectedAnswers.includes(option.clientId);
              const isCorrect = option.isCorrect;

              // Xác định class CSS cho option
              let optionClassName = 'option-card';
              let showFeedbackIcon = false;
              let feedbackIcon = null;

              if (showResult) {
                // Chế độ hiển thị kết quả
                if (isSelected && isCorrect) {
                  optionClassName += ' correct';
                  showFeedbackIcon = true;
                  feedbackIcon = (
                    <CheckOutlined className="feedback-icon correct" />
                  );
                } else if (isSelected && !isCorrect) {
                  optionClassName += ' incorrect';
                  showFeedbackIcon = true;
                  feedbackIcon = (
                    <CloseOutlined className="feedback-icon incorrect" />
                  );
                } else if (!isSelected && isCorrect) {
                  showFeedbackIcon = true;
                  feedbackIcon = (
                    <CheckOutlined className="feedback-icon correct" />
                  );
                }
              } else if (isSubmitted) {
                // Chế độ đã submit nhưng chưa hiển thị kết quả
                if (isCorrect) {
                  optionClassName += ' correct';
                  showFeedbackIcon = true;
                  feedbackIcon = (
                    <CheckOutlined className="feedback-icon correct" />
                  );
                } else if (isSelected) {
                  optionClassName += ' incorrect';
                  showFeedbackIcon = true;
                  feedbackIcon = (
                    <CloseOutlined className="feedback-icon incorrect" />
                  );
                }
              } else {
                // Chế độ bình thường
                if (isSelected) {
                  optionClassName += ' selected';
                }
              }

              return (
                <Col span={12} key={option.clientId}>
                  <div
                    className={optionClassName}
                    onClick={() => {
                      if (!showResult && !isSubmitted && !disabled) {
                        handleAnswerSelect(option.clientId);
                      }
                    }}
                    style={{
                      cursor:
                        !showResult && !isSubmitted && !disabled
                          ? 'pointer'
                          : 'default',
                    }}
                  >
                    <div className="option-content">
                      <Checkbox
                        checked={isSelected}
                        disabled={showResult || isSubmitted || disabled}
                        onChange={(e) => {
                          if (!showResult && !isSubmitted && !disabled) {
                            e.stopPropagation();
                            handleAnswerSelect(option.clientId);
                          }
                        }}
                      />
                      <span
                        className="option-text"
                        dangerouslySetInnerHTML={{
                          __html: option.content,
                        }}
                      ></span>
                    </div>
                    {showFeedbackIcon && (
                      <div className="option-feedback">{feedbackIcon}</div>
                    )}
                  </div>
                </Col>
              );
            })}
        </Row>
      </div>
    );
  };

  return (
    <BaseQuestionPreviewComponent
      question={question}
      questionIndex={questionIndex}
      hideFeedback={options.hideFeedback}
      showResult={showResult}
      isCorrect={isCorrect}
      isAnswered={isAnswered}
      cardClassName="multi-select-card"
      guidanceText="Chọn tất cả các đáp án đúng."
      renderContent={renderContent}
      renderInteraction={renderInteraction}
      isMarked={isMarked}
      onToggleMark={onToggleMark}
    />
  );
};

export default memo(MultiSelectQuizComponent);
