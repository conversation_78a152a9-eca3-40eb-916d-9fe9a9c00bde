// hooks/useMatchingResize.ts
import { useEffect, useRef, useCallback, useState } from 'react';

export interface UseMatchingResizeOptions {
  debounceDelay?: number;
  throttleDelay?: number;
  enableLogging?: boolean;
}

export const useMatchingResize = (
  onResize: () => void,
  options: UseMatchingResizeOptions = {}
) => {
  const {
    debounceDelay = 16,
    throttleDelay = 8,
    enableLogging = false,
  } = options;

  const containerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const debounceTimeoutRef = useRef<number | null>(null);
  const throttleTimeoutRef = useRef<number | null>(null);
  const lastResizeTimeRef = useRef<number>(0);
  const isResizingRef = useRef<boolean>(false);

  const [isResizing, setIsResizing] = useState(false);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  // Throttled resize handler
  const throttledResize = useCallback(() => {
    const now = Date.now();

    if (now - lastResizeTimeRef.current < throttleDelay) {
      if (!throttleTimeoutRef.current) {
        throttleTimeoutRef.current = setTimeout(() => {
          throttledResize();
          throttleTimeoutRef.current = null;
        }, throttleDelay);
      }
      return;
    }

    lastResizeTimeRef.current = now;

    if (!isResizingRef.current) {
      isResizingRef.current = true;
      setIsResizing(true);

      if (enableLogging) {
        console.log('Matching resize started');
      }
    }

    // Clear existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new debounce timeout
    debounceTimeoutRef.current = setTimeout(() => {
      onResize();
      isResizingRef.current = false;
      setIsResizing(false);

      if (enableLogging) {
        console.log('Matching resize completed');
      }
    }, debounceDelay);
  }, [onResize, debounceDelay, throttleDelay, enableLogging]);

  // Update container size
  const updateContainerSize = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    const rect = container.getBoundingClientRect();
    const newSize = { width: rect.width, height: rect.height };

    setContainerSize((prevSize) => {
      // Only update if size actually changed significantly (> 1px)
      if (
        Math.abs(prevSize.width - newSize.width) > 1 ||
        Math.abs(prevSize.height - newSize.height) > 1
      ) {
        if (enableLogging) {
          console.log('Container size changed:', newSize);
        }
        return newSize;
      }
      return prevSize;
    });
  }, [enableLogging]);

  // Setup resize observer
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Initial size update
    updateContainerSize();

    // Create resize observer
    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        throttledResize();
        updateContainerSize();
      }
    });

    resizeObserverRef.current.observe(container);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }

      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, [throttledResize, updateContainerSize]);

  // Window resize fallback
  useEffect(() => {
    const handleWindowResize = () => {
      throttledResize();
      updateContainerSize();
    };

    window.addEventListener('resize', handleWindowResize);
    return () => window.removeEventListener('resize', handleWindowResize);
  }, [throttledResize, updateContainerSize]);

  // Force resize trigger
  const forceResize = useCallback(() => {
    updateContainerSize();
    onResize();
  }, [onResize, updateContainerSize]);

  // Check if container dimensions are valid
  const isValidSize = containerSize.width > 0 && containerSize.height > 0;

  return {
    containerRef,
    isResizing,
    containerSize,
    isValidSize,
    forceResize,
  };
};
