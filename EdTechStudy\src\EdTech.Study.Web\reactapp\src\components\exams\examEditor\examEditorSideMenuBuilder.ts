import { IBookMarkItem } from '../../../interfaces/commonInterfaces';
import { TreeItemType } from './ExamEditorSideMenu';

// Function to transform flat items list to a hierarchical structure
export const buildTreeStructure = (items: IBookMarkItem[]): TreeItemType[] => {
  const map: Record<string, TreeItemType> = {};
  const roots: TreeItemType[] = [];

  const orderedItems = items.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
  // Create nodes with children array
  orderedItems.forEach((item) => {
    map[item.id] = { ...item, children: [] };
  });

  // Assign children to parents
  orderedItems.forEach((item) => {
    if (item.parentId && map[item.parentId]) {
      // This item has a parent, add it to parent's children
      map[item.parentId].children.push(map[item.id]);
    } else {
      // This is a root level item
      roots.push(map[item.id]);
    }
  });

  return roots;
};
