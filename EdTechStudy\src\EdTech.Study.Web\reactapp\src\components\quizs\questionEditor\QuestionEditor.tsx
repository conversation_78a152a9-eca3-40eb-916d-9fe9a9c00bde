import React, {
  memo,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  useTransition,
} from 'react';
import {
  Modal,
  Button,
  Space,
  Divider,
  Select,
  message,
  Tag,
  Input,
} from 'antd';
import {
  BaseQuestion,
  getQuestionComponentRegistry,
  PracticeEngineContext,
  QuestionComponentBaseProps,
  QuestionComponentOption,
  questionTypeShortDescription,
} from '../../../interfaces/quizs/questionBase';
import {
  CheckCircleOutlined,
  EditOutlined,
  SendOutlined,
  CloseCircleOutlined,
  TagsOutlined,
  ProfileOutlined,
  PlusOutlined,
  BookFilled,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import './QuestionEditor.css';
import './QuestionEditorMetadata.css';

import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';
import {
  ExamStatus,
  ExamSourceType,
} from '../../../interfaces/exams/examEnums';
import FocusingContext from '../../common/Focusing/FocusingContext';
import HashHelper from '../../../utils/HashHelper';
import LoadingScreen from '../../common/Loading/LoadingScreen';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import questionDraftAdapter from '../../../utils/adapters/questionDraftAdapter';
import { Subject } from '../../../interfaces/lessons/subject';
import { getQuestionTypeDisplayVNFromString } from '../../../utils/questionUtil';
import practiceLocalization from '../localization';
import DelayRender from '../../common/Loading/DelayRender';

export interface QuestionEditorOption extends QuestionComponentOption {
  // ẩn nút thay đổi trạng thái
  hideStatusButton?: boolean;
  hideStatusTag?: boolean;
  hideSourceTypeButton?: boolean;
}

export interface QuestionEditorProps {
  visible: boolean;
  panels?: string[];
  question: BaseQuestion | null;
  title?: string;
  subjects: Subject[];
  grades?: LessonGrade[];

  currentPosition?: number;
  totalQuestion?: number;
  options?: QuestionEditorOption;
  onCancel: () => void;
  onSave: (updatedQuestion: BaseQuestion) => void;
  onNext?: () => any;
  onPrev?: () => any;
}

// Define the status configuration outside the component
const STATUS_CONFIG = {
  [ExamStatus.Published]: {
    color: 'success',
    text: 'Công bố',
    icon: <CheckCircleOutlined />,
  },
  [ExamStatus.Approved]: {
    color: 'processing',
    text: 'Đã duyệt',
    icon: <CheckCircleOutlined />,
  },
  [ExamStatus.Submitted]: {
    color: 'warning',
    text: 'Đang chờ duyệt',
    icon: <SendOutlined />,
  },
  [ExamStatus.Rejected]: {
    color: 'error',
    text: 'Đã từ chối',
    icon: <CloseCircleOutlined />,
  },
  [ExamStatus.Draft]: {
    color: 'default',
    text: 'Soạn thảo',
    icon: <EditOutlined />,
  },
} as const;

const QuestionEditor: React.FC<QuestionEditorProps> = ({
  question,
  title = '',
  subjects,
  grades,
  options = {
    hideDeleteButton: true,
    hideSaveButton: true,
    hideFeedback: false,
    hideStatusButton: true,
    hideStatusTag: false,
    hideSourceTypeButton: true,
  },
}) => {
  const cacheQuestionComponent = useRef<
    {
      id: string;
      component:
        | React.FC<QuestionComponentBaseProps>
        | React.LazyExoticComponent<React.FC<QuestionComponentBaseProps>>;
    }[]
  >([]);
  const { handleMarkQuestion, markedIds } = useContext(PracticeEngineContext);
  const [editedQuestion, setEditedQuestion] = useState<BaseQuestion | null>(
    null
  );
  const [selectedQuestionType, setSelectedQuestionType] = useState<string>(
    question?.questionType
      ? questionDraftAdapter.mapServerTypeToClientType(question.questionType)
      : ''
  );
  const [selectedSubject, setSelectedSubject] = useState<string>();

  // Add gradeId state
  const [selectedGrade, setSelectedGrade] = useState<string>();

  // Add source type state
  const [selectedSourceType, setSelectedSourceType] = useState<ExamSourceType>(
    question?.sourceType || ExamSourceType.Default
  );

  // State for tags and topics
  const [tags, setTags] = useState<string[]>([]);
  const [topics, setTopics] = useState<string[]>([]);
  const [newTag, setNewTag] = useState<string>('');
  const [newTopic, setNewTopic] = useState<string>('');
  const [showMetadata, setShowMetadata] = useState<boolean>(false);
  const { onFocusId, onBlur } = useContext(FocusingContext);
  const [isPending, startTransition] = useTransition();
  const { handlePendingState } = useContext(PracticeEngineContext);
  const { handleUpdateQuestion, isPending: isPendingUpdate } =
    useUpdateQuestion();

  // Initialize the edited question when the modal becomes visible or question changes
  useEffect(() => {
    if (question) {
      setEditedQuestion({ ...question });
      setSelectedQuestionType(question.type);
      setSelectedSubject(question.subjectId);
      setSelectedGrade(question.gradeId);
      setSelectedSourceType(question.sourceType || ExamSourceType.Default);

      // Initialize tags and topics
      try {
        const tagsData = Array.isArray(question.tags)
          ? [...question.tags]
          : question.tags
          ? question.tags.split(',').map((tag) => tag.trim())
          : [];
        setTags(tagsData);
      } catch (error) {
        console.error(error);
      }
      try {
        const topicsData = Array.isArray(question.metadata?.topics)
          ? [...question.metadata.topics]
          : question.topics
          ? (question.topics as string).split(',').map((topic) => topic.trim())
          : [];
        setTopics(topicsData);
      } catch (error) {
        console.error(error);
      }
    }
  }, [question]);

  // Handle subject change
  const handleSubjectChange = useCallback(
    (subjectId: string) => {
      if (!editedQuestion) return;

      const newQuestion = {
        ...editedQuestion,
        subjectId: subjectId,
        subject: subjects.find((s) => s.id === subjectId),
      };

      setEditedQuestion(newQuestion);
      setSelectedSubject(subjectId);
      handleUpdateQuestion(newQuestion);
    },
    [editedQuestion, subjects]
  );

  // Add grade change handler
  const handleGradeChange = useCallback(
    (gradeId: string) => {
      if (!editedQuestion) return;

      const newQuestion = {
        ...editedQuestion,
        gradeId: gradeId,
        grade: grades?.find((g) => g.id === gradeId),
      };

      setEditedQuestion(newQuestion);
      setSelectedGrade(gradeId);
      handleUpdateQuestion(newQuestion);
    },
    [editedQuestion, grades]
  );

  // Add source type change handler
  const handleSourceTypeChange = useCallback(
    (value: string) => {
      setSelectedSourceType(Number(value) as ExamSourceType);
      const updatedQuestion = {
        ...editedQuestion,
        sourceType: Number(value) as ExamSourceType,
      } as BaseQuestion;
      handleUpdateQuestion(updatedQuestion);
    },
    [editedQuestion, handleUpdateQuestion]
  );

  // Handle tags management
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      const updatedTags = [...tags, newTag.trim()];
      setTags(updatedTags);

      if (editedQuestion) {
        setEditedQuestion({
          ...editedQuestion,
          tags: updatedTags,
        });
      }

      setNewTag('');

      handleUpdateQuestion({
        ...editedQuestion,
        tags: updatedTags,
      } as BaseQuestion);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(updatedTags);

    if (editedQuestion) {
      setEditedQuestion({
        ...editedQuestion,
        tags: updatedTags,
      });

      handleUpdateQuestion({
        ...editedQuestion,
        tags: updatedTags,
      } as BaseQuestion);
    }
  };

  // Handle topics management
  const handleAddTopic = () => {
    if (newTopic.trim() && !topics.includes(newTopic.trim())) {
      const updatedTopics = [...topics, newTopic.trim()];
      setTopics(updatedTopics);

      if (editedQuestion) {
        const updatedMetadata = {
          ...editedQuestion.metadata,
          topics: updatedTopics,
        };

        setEditedQuestion({
          ...editedQuestion,
          topics: updatedTopics.join(','),
          metadata: updatedMetadata,
        });

        handleUpdateQuestion({
          ...editedQuestion,
          topics: updatedTopics.join(','),
          metadata: updatedMetadata,
        } as BaseQuestion);
      }

      setNewTopic('');
    }
  };

  const handleRemoveTopic = (topicToRemove: string) => {
    const updatedTopics = topics.filter((topic) => topic !== topicToRemove);
    setTopics(updatedTopics);

    if (editedQuestion) {
      const updatedMetadata = {
        ...editedQuestion.metadata,
        topics: updatedTopics,
      };

      setEditedQuestion({
        ...editedQuestion,
        topics: updatedTopics.join(','),
        metadata: updatedMetadata,
      });

      handleUpdateQuestion({
        ...editedQuestion,
        topics: updatedTopics.join(','),
        metadata: updatedMetadata,
      } as BaseQuestion);
    }
  };

  // Toggle metadata panel
  const toggleMetadataPanel = useCallback(() => {
    setShowMetadata((prev) => !prev);
  }, []);

  // Handle question type change
  const handleQuestionTypeChange = useCallback(
    (newType: string) => {
      if (newType === selectedQuestionType) {
        return; // No change needed
      }

      // Confirm before changing type
      Modal.confirm({
        title: 'Thay đổi loại câu hỏi',
        content:
          'Thay đổi loại câu hỏi có thể làm mất một số dữ liệu. Bạn có chắc chắn muốn tiếp tục?',
        okText: 'Đồng ý',
        cancelText: 'Hủy',
        onOk: async () => {
          const QuestionTemplateFactory = await import(
            '../practiceEngines/questionTemplates'
          ).then((module) => module.QuestionTemplateFactory);
          const factory = QuestionTemplateFactory.getInstance();

          // Create new question template of the selected type
          const newQuestionTemplate = factory.create(newType);

          if (!newQuestionTemplate) {
            message.error('Không thể tạo mẫu câu hỏi mới');
            return;
          }

          // Preserve some properties from the original question
          let updatedQuestion = {
            ...newQuestionTemplate,
            id: editedQuestion?.id || '',
            clientId: editedQuestion?.clientId || newQuestionTemplate.clientId,
            title: editedQuestion?.title || newQuestionTemplate.title,
            description: editedQuestion?.content || newQuestionTemplate.content,
            content: editedQuestion?.content || newQuestionTemplate.content,
            subjectId:
              editedQuestion?.subjectId || newQuestionTemplate.subjectId,
            points: editedQuestion?.points || newQuestionTemplate.points,
            statusEntity: editedQuestion?.statusEntity || ExamStatus.Draft,
            tags: tags,
            topics: topics.join(','),
            options: editedQuestion?.options,
            metadata: {
              ...editedQuestion?.metadata,
              topics: topics,
            },
            type: newType,
            sourceType: editedQuestion?.sourceType || ExamSourceType.Default,
          } as BaseQuestion;

          switch (newType) {
            case practiceLocalization.quiz:
            case practiceLocalization.singlechoice:
              let isCorrectId = editedQuestion?.options?.find(
                (option) => option.isCorrect
              )?.clientId;
              updatedQuestion.options = editedQuestion?.options?.map(
                (option) => ({
                  ...option,
                  isCorrect: option.clientId === isCorrectId,
                })
              );
              break;
            case practiceLocalization.fillblanks:
              if (!editedQuestion?.content?.includes('___')) {
                updatedQuestion.blanks = [];
              }
              break;
            case practiceLocalization.essay:
              updatedQuestion.options = [];
              updatedQuestion.blanks = [];
              updatedQuestion.content = 'Nội dung câu hỏi tự luận';
              updatedQuestion.title = 'Câu hỏi tự luận';
              break;
          }

          // Update the state
          startTransition(() => {
            setEditedQuestion(updatedQuestion);
            setSelectedQuestionType(newType);
            message.success(
              `Đã chuyển đổi sang câu hỏi dạng ${getQuestionTypeDisplayVNFromString(
                newType
              )}`
            );
          });

          handleUpdateQuestion({
            ...updatedQuestion,
            clientId: editedQuestion?.clientId,
            parentId: editedQuestion?.parentId,
          });
        },
      });
    },
    [selectedQuestionType, editedQuestion, tags, topics, handleUpdateQuestion]
  );

  // Get status tag color and text
  const getStatusTag = useCallback(() => {
    if (editedQuestion?.statusEntity === undefined) return null;

    const { statusEntity } = editedQuestion;
    const status =
      STATUS_CONFIG[statusEntity] ?? STATUS_CONFIG[ExamStatus.Draft];
    return (
      <Tag color={status.color} icon={status.icon} className="status-tag">
        {status.text}
      </Tag>
    );
  }, [editedQuestion]);

  function handleApproveRejectQuestion(status: ExamStatus): void {
    if (editedQuestion?.statusEntity === status) return;

    const updatedQuestion = {
      ...editedQuestion,
      statusEntity: status,
    } as BaseQuestion;
    handleUpdateQuestion(updatedQuestion);
  }

  const questionComponent = useMemo(() => {
    if (isPending) return <LoadingScreen />;
    if (!question) return null;
    const componentId = HashHelper.computeHash([
      question.clientId,
      question.type,
    ]);
    let existed = false;
    let QuestionComponentRenderer:
      | React.FC<QuestionComponentBaseProps>
      | React.LazyExoticComponent<React.FC<QuestionComponentBaseProps>>
      | undefined = undefined;
    const cachedComponent = cacheQuestionComponent.current.find(
      (c) => c.id === componentId
    );
    if (cachedComponent) {
      existed = true;
      QuestionComponentRenderer = cachedComponent.component;
    } else {
      QuestionComponentRenderer = getQuestionComponentRegistry().getComponent(
        question.type
      )?.component;
    }

    if (!QuestionComponentRenderer)
      return (
        <div className="question-not-found">
          Không tìm thấy loại câu hỏi phù hợp:{' '}
          {getQuestionTypeDisplayVNFromString(question.type)}
        </div>
      );
    if (!existed) {
      cacheQuestionComponent.current.push({
        id: componentId,
        component: QuestionComponentRenderer,
      });
      if (cacheQuestionComponent.current.length > 3)
        cacheQuestionComponent.current =
          cacheQuestionComponent.current.slice(1);
    }

    return (
      <div id={question.clientId} className="single-question-container">
        <React.Suspense fallback={<LoadingScreen />}>
          <QuestionComponentRenderer
            id={componentId}
            question={question}
            configMode={true}
            htmlAttributes={{
              className: 'card-component-ghost tailwind-m-0 tailwind-p-0',
            }}
            options={options}
          />
        </React.Suspense>
      </div>
    );
  }, [
    isPending,
    question,
    question?.clientId,
    question?.type,
    selectedQuestionType,
  ]);

  useEffect(() => {
    if (isPendingUpdate) {
      handlePendingState?.(true);
    } else {
      handlePendingState?.(false);
    }
  }, [isPendingUpdate]);

  // If no question is available yet, show nothing
  if (!editedQuestion) {
    return null;
  }

  return (
    <div className="question-editor">
      <div className="question-editor-header tailwind-pb-2">
        <div className="title-with-tag">
          {title && title.length > 0 && (
            <span className="limited-title" title={title}>
              {title.length > 50 ? title.substring(0, 50) + '...' : title}
            </span>
          )}
          {!options?.hideStatusTag && getStatusTag()}
        </div>
        <Space wrap className="tailwind-mr-4">
          {!options?.hideStatusButton && (
            <div className="tailwind-flex tailwind-gap-2 tailwind-mx-2">
              <Button
                type="primary"
                icon={<CheckOutlined />}
                disabled={editedQuestion.statusEntity === ExamStatus.Approved}
                onClick={() => handleApproveRejectQuestion(ExamStatus.Approved)}
              >
                Duyệt
              </Button>
              <Button
                type="primary"
                danger
                icon={<CloseOutlined />}
                disabled={editedQuestion.statusEntity === ExamStatus.Draft}
                onClick={() => handleApproveRejectQuestion(ExamStatus.Draft)}
              >
                Soạn thảo
              </Button>
            </div>
          )}
          <Select
            style={{ width: 200 }}
            value={selectedQuestionType}
            options={questionTypeShortDescription}
            placeholder="Chọn loại câu hỏi"
            onChange={handleQuestionTypeChange}
            onFocus={() => onFocusId && onFocusId(editedQuestion.clientId)}
            onBlur={() => onBlur && onBlur(editedQuestion.clientId)}
          />
          <Select
            value={selectedSubject}
            onChange={handleSubjectChange}
            style={{ width: 150 }}
            placeholder="Chọn môn học"
            onFocus={() => onFocusId && onFocusId(editedQuestion.clientId)}
            onBlur={() => onBlur && onBlur(editedQuestion.clientId)}
          >
            {subjects.map((subject) => (
              <Select.Option key={subject.id} value={subject.id}>
                {subject.name}
              </Select.Option>
            ))}
          </Select>
          <Select
            value={selectedGrade}
            onChange={handleGradeChange}
            style={{ width: 150 }}
            placeholder="Chọn lớp"
            onFocus={() => onFocusId && onFocusId(editedQuestion.clientId)}
            onBlur={() => onBlur && onBlur(editedQuestion.clientId)}
          >
            {grades?.map((grade) => (
              <Select.Option key={grade.id} value={grade.id}>
                {grade.name}
              </Select.Option>
            ))}
          </Select>
          {options &&
            options.hideSourceTypeButton != undefined &&
            options.hideSourceTypeButton !== true && (
              <Select
                value={selectedSourceType.toString()}
                onChange={handleSourceTypeChange}
                style={{ width: 150 }}
                placeholder="Chọn nguồn gốc"
                onFocus={() => onFocusId && onFocusId(editedQuestion.clientId)}
                onBlur={() => onBlur && onBlur(editedQuestion.clientId)}
              >
                <Select.Option
                  value={ExamSourceType.Default.toString()}
                  key={ExamSourceType.Default.toString()}
                >
                  Mặc định
                </Select.Option>
                <Select.Option
                  value={ExamSourceType.Banked.toString()}
                  key={ExamSourceType.Banked.toString()}
                >
                  Ngân hàng câu hỏi
                </Select.Option>
              </Select>
            )}
          <Button
            icon={<TagsOutlined />}
            onClick={toggleMetadataPanel}
            type={showMetadata ? 'primary' : 'default'}
          >
            Thẻ & Chủ đề
          </Button>
          {handleMarkQuestion && (
            <>
              <Button
                icon={<BookFilled />}
                onClick={(_) => {
                  handleMarkQuestion(
                    editedQuestion.clientId,
                    !markedIds?.includes(editedQuestion.clientId)
                  );
                }}
                type={
                  markedIds?.includes(editedQuestion.clientId)
                    ? 'primary'
                    : 'default'
                }
              >
                Đánh dấu
              </Button>
            </>
          )}
        </Space>
      </div>
      <div className="question-editor-content">
        {showMetadata && (
          <div className="metadata-panel">
            <h3>Thông tin bổ sung</h3>
            <Divider />

            <div className="metadata-section">
              <h4>
                <TagsOutlined /> Thẻ
              </h4>
              <div className="tags-container">
                {tags.map((tag) => (
                  <Tag
                    key={tag}
                    closable
                    onClose={(e) => {
                      e.preventDefault();
                      handleRemoveTag(tag);
                    }}
                  >
                    {tag}
                  </Tag>
                ))}
              </div>
              <Input
                placeholder="Thêm thẻ mới"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onPressEnter={handleAddTag}
                suffix={
                  <PlusOutlined
                    onClick={handleAddTag}
                    style={{ cursor: 'pointer' }}
                  />
                }
                style={{ marginTop: '8px' }}
                onFocus={() => onFocusId && onFocusId(editedQuestion.clientId)}
                onBlur={() => onBlur && onBlur(editedQuestion.clientId)}
              />
            </div>

            <div className="metadata-section">
              <h4>
                <ProfileOutlined /> Chủ đề
              </h4>
              <div className="topics-container">
                {topics.map((topic) => (
                  <Tag
                    key={topic}
                    closable
                    color="blue"
                    onClose={(e) => {
                      e.preventDefault();
                      handleRemoveTopic(topic);
                    }}
                  >
                    {topic}
                  </Tag>
                ))}
              </div>
              <Input
                placeholder="Thêm chủ đề mới"
                value={newTopic}
                onChange={(e) => setNewTopic(e.target.value)}
                onPressEnter={handleAddTopic}
                suffix={
                  <PlusOutlined
                    onClick={handleAddTopic}
                    style={{ cursor: 'pointer' }}
                  />
                }
                style={{ marginTop: '8px' }}
                onFocus={() => onFocusId && onFocusId(editedQuestion.clientId)}
                onBlur={() => onBlur && onBlur(editedQuestion.clientId)}
              />
            </div>
          </div>
        )}
        <div
          className="question-split-view"
          style={{ position: 'relative', display: 'flex' }}
        >
          <div
            className="question-config-container"
            style={{
              overflow: 'auto',
              width: `100%`,
              paddingLeft: '12px',
              minHeight: '500px',
            }}
          >
            <DelayRender delay={0.5}>{questionComponent}</DelayRender>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(QuestionEditor);
