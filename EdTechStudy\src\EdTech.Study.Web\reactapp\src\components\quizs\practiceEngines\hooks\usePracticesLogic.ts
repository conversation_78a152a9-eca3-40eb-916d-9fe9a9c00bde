import { useEffect, useState, useTransition, useRef } from 'react';
import {
  getQuestionComponentRegistry,
  QuestionComponentBaseProps,
} from '../../../../interfaces/quizs/questionBase';
import practiceLocalization from '../../localization';

import {
  QuestionTemplateFactory,
  createQuizQuestionTemplate,
  createMatchingQuestionTemplate,
  createFillBlanksQuestionTemplate,
  createMultiSelectQuestionTemplate,
  createEssayQuestionTemplate,
} from '../questionTemplates';
import React from 'react';
import { matchingCheckCorrectAnswer, MatchingComponent } from '../../matching';
import { fillBlanksCheckCorrectAnswer } from '../../fillblanks';
import { multiSelectCheckCorrectAnswer } from '../../multiselect';

import { createQuestionBankTemplate } from '../../questionBank/questionBankTemplate';
import { quizCheckCorrectAnswer } from '../../quiz/quizUtils';
import { essayCheckCorrectAnswer } from '../../essay';
import QuizComponent from '../../quiz/QuizComponent';
import FillBlanksComponent from '../../fillblanks/FillBlanksComponent';
import MultiSelectQuizComponent from '../../multiselect/MultiSelectQuizComponent';
import EssayComponent from '../../essay/EssayComponent';

export interface PracticesLogicProps {
  consumerId: string;
  onInitialized?: () => void;
}

export interface PracticesLogicResult {
  isPending: boolean;
  isInitialized: boolean;
  error: Error | null;
}

const usePracticesLogic = (
  props: PracticesLogicProps
): PracticesLogicResult => {
  const setupInitialized = useRef<Record<string, boolean>>({});

  const [isPending, startTransition] = useTransition();
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Get the registry instance once at the top level
    if (setupInitialized.current[props.consumerId ?? '']) return;
    const initializePracticeEngine = async () => {
      try {
        // Create a Promise that resolves when all registrations are complete
        await new Promise<void>((resolve, reject) => {
          startTransition(() => {
            try {
              // For creating new instances
              const factory = QuestionTemplateFactory.getInstance();
              const questionRegistry = getQuestionComponentRegistry();

              factory.register(
                practiceLocalization.quiz,
                createQuizQuestionTemplate
              );
              factory.register(
                practiceLocalization.singlechoice,
                createQuizQuestionTemplate
              );
              factory.register(
                practiceLocalization.matching,
                createMatchingQuestionTemplate
              );
              factory.register(
                practiceLocalization.fillblanks,
                createFillBlanksQuestionTemplate
              );
              factory.register(
                practiceLocalization.multiselect,
                createMultiSelectQuestionTemplate
              );
              factory.register(
                practiceLocalization.multiplechoice,
                createMultiSelectQuestionTemplate
              );
              factory.register(
                practiceLocalization.essay,
                createEssayQuestionTemplate
              );
              factory.register(
                practiceLocalization.questionbank,
                createQuestionBankTemplate
              );

              // For rendering Components
              questionRegistry.register(practiceLocalization.quiz, {
                name: practiceLocalization.quiz,
                description: practiceLocalization.quiz_description,
                component: QuizComponent as React.LazyExoticComponent<
                  React.FC<QuestionComponentBaseProps>
                >,
                checkCorrectAnswer: quizCheckCorrectAnswer,
              });
              questionRegistry.register(practiceLocalization.singlechoice, {
                name: practiceLocalization.singlechoice,
                description: practiceLocalization.quiz_description,
                component: QuizComponent as React.LazyExoticComponent<
                  React.FC<QuestionComponentBaseProps>
                >,
                checkCorrectAnswer: quizCheckCorrectAnswer,
              });
              questionRegistry.register(practiceLocalization.matching, {
                name: practiceLocalization.matching,
                description: practiceLocalization.matching_description,
                component: MatchingComponent as React.LazyExoticComponent<
                  React.FC<QuestionComponentBaseProps>
                >,
                checkCorrectAnswer: matchingCheckCorrectAnswer,
              });
              questionRegistry.register(practiceLocalization.fillblanks, {
                name: practiceLocalization.fillblanks,
                description: practiceLocalization.fillblanks_description,
                component: FillBlanksComponent as React.LazyExoticComponent<
                  React.FC<QuestionComponentBaseProps>
                >,
                checkCorrectAnswer: fillBlanksCheckCorrectAnswer,
              });
              questionRegistry.register(practiceLocalization.multiselect, {
                name: practiceLocalization.multiselect,
                description: practiceLocalization.multiselect_description,
                component:
                  MultiSelectQuizComponent as React.LazyExoticComponent<
                    React.FC<QuestionComponentBaseProps>
                  >,
                checkCorrectAnswer: multiSelectCheckCorrectAnswer,
              });
              questionRegistry.register(practiceLocalization.multiplechoice, {
                name: practiceLocalization.multiplechoice,
                description: practiceLocalization.multiselect_description,
                component:
                  MultiSelectQuizComponent as React.LazyExoticComponent<
                    React.FC<QuestionComponentBaseProps>
                  >,
                checkCorrectAnswer: multiSelectCheckCorrectAnswer,
              });
              questionRegistry.register(practiceLocalization.essay, {
                name: practiceLocalization.essay,
                description: practiceLocalization.essay_description,
                component: EssayComponent as React.LazyExoticComponent<
                  React.FC<QuestionComponentBaseProps>
                >,
                checkCorrectAnswer: essayCheckCorrectAnswer,
              });
              resolve();
            } catch (err) {
              reject(
                err instanceof Error
                  ? err
                  : new Error('Failed to initialize practice engine')
              );
            }
          });
        });

        setIsInitialized(true);
        console.log('initialized', props.consumerId);
        setupInitialized.current[props.consumerId] = true;
        props?.onInitialized?.();
      } catch (err) {
        setError(
          err instanceof Error
            ? err
            : new Error('Failed to initialize practice engine')
        );
      }
    };

    initializePracticeEngine();
  }, [props.consumerId]);

  return {
    isPending,
    isInitialized,
    error,
  };
};

export default usePracticesLogic;
