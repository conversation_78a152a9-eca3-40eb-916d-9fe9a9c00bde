import { MatchingItemType } from '../exams/examEnums';
import { BaseAnswer, BaseQuestion } from './questionBase';

// Interface for Matching items
export interface MatchingItem {
  id?: string;
  clientId: string;
  content: string;
  type: MatchingItemType;
  order: number;
  matchingIds?: string[];
}

export interface MatchingQuestionAnswer extends BaseAnswer {
  premiseId: string;
  responseId: string;
  score?: number;
  feedback?: string;
}

// Complete matching question interface
export interface MatchingQuestion extends BaseQuestion {
  type: 'matching';
  matchingItems?: MatchingItem[]; // Chứa cả item bên phải và bên trái (phân biệt bằng type)
  matchingAnswers: MatchingQuestionAnswer[];
  userSelect?: MatchingQuestionAnswer[];
}
