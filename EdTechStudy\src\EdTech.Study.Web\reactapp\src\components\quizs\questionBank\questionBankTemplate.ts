import { Guid } from 'guid-typescript';
import {
  BaseQuestion,
  QuestionType,
  QuizQuestion,
} from '../../../interfaces/quizs/questionBase';

import { FillBlanksQuestion } from '../fillblanks';
import { MultiSelectQuestion } from '../../../interfaces/quizs/multiSelectQuiz.interface';
import {
  ContentFormatType,
  MatchingItemType,
  MatchingItemTypeString,
} from '../../../interfaces/exams/examEnums';
import { Subject } from '../../../interfaces/lessons/subject';
import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';
import practiceLocalization from '../localization';
import { mapQuestionStatus } from '../../../utils/adapters/questionDraftAdapter';
import { getQuestionTypeFromString } from '../../../utils/questionUtil';
import { EssayQuestion } from '../../../interfaces/quizs/essay.interface';
import { MatchingQuestion } from '../../../interfaces/quizs/mapping.interface';

// Add this enum to match the backend QuestionType
export const createQuestionBankTemplate = (
  question: QuestionDto
): BaseQuestion => {
  const questionType = question.QuestionType
    ? getQuestionTypeFromString(question.QuestionType)
    : question.questionType;
  const questionStatus = mapQuestionStatus(question.Status ?? 0);
  switch (questionType) {
    case QuestionType.SingleChoice:
      return {
        id: question.Id,
        clientId:
          (question.Id || question.ClientId) ?? Guid.create().toString(),
        type: practiceLocalization.quiz,
        questionId: question.QuestionId,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi 1 đáp án',
        content: question.Content,
        questionType: QuestionType.SingleChoice,
        options: question.Options?.map((o) => ({
          id: o.Id,
          clientId: o.Id,
          text: o.Content,
          content: o.Content,
          isCorrect: o.IsCorrect,
          contentFormat: o.ContentFormat,
          order: o.Order,
        })).sort((a, b) => a.order - b.order),
        status: questionStatus,
        statusEntity: questionStatus,
        order: question.Order,
      } as QuizQuestion;

    case QuestionType.Essay:
      return {
        id: question.Id,
        clientId:
          (question.Id || question.ClientId) ?? Guid.create().toString(),
        type: practiceLocalization.essay,
        questionId: question.QuestionId,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title,
        content: question.Content,
        questionType: QuestionType.Essay,
        correctAnswer: '',
        status: questionStatus,
        statusEntity: questionStatus,
        order: question.Order,
        explanation: question.Explanation,
      } as EssayQuestion;

    case QuestionType.FillInBlank:
      // Todo
      return {
        id: question.Id,
        clientId:
          (question.Id || question.ClientId) ?? Guid.create().toString(),
        type: practiceLocalization.fillblanks,
        questionId: question.QuestionId,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi điền ô trống',
        content: question.Content,
        questionType: QuestionType.FillInBlank,
        options: [],
        blanks:
          question.FillInBlankAnswers?.map((a) => ({
            id: a.Id,
            clientId: a.Id,
            correctAnswer: a.CorrectAnswers.join(', '),
            alternativeAnswers: a.CorrectAnswers,
            hint: '',
            order: a.Order,
          })).sort((a, b) => a.order - b.order) || [],
        explanation: question.Explanation,
        status: questionStatus,
        statusEntity: questionStatus,
        order: question.Order,
      } as FillBlanksQuestion;
    case QuestionType.MultipleChoice:
      return {
        id: question.Id,
        clientId:
          (question.Id || question.ClientId) ?? Guid.create().toString(),
        type: practiceLocalization.multiselect,
        questionId: question.QuestionId,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi nhiều đáp án',
        content: question.Content,
        questionType: QuestionType.MultipleChoice,
        options: question.Options?.map((o) => ({
          id: o.Id,
          clientId: o.Id,
          text: o.Content,
          content: o.Content,
          contentFormat: ContentFormatType.Html,
          isCorrect: o.IsCorrect,
          order: o.Order,
        })).sort((a, b) => a.order - b.order),
        status: questionStatus,
        statusEntity: questionStatus,
        order: question.Order,
      } as MultiSelectQuestion;

    case QuestionType.Matching:
      // Todo
      return {
        id: question.Id,
        clientId:
          (question.Id || question.ClientId) ?? Guid.create().toString(),
        type: practiceLocalization.matching,
        questionText: '',
        questionId: question.QuestionId,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi nối',
        content: question.Content,
        questionType: QuestionType.Matching,
        matchingItems: question.MatchingItems.map((i) => ({
          id: i.Id,
          clientId: i.Id,
          content: i.Content,
          type:
            i.Type === MatchingItemTypeString.Premise
              ? MatchingItemType.Premise
              : MatchingItemType.Response,
          order: 0,
        })),
        matchingAnswers: question.MatchingAnswers.map((a) => ({
          id: a.Id,
          clientId: a.Id,
          premiseId: a.PremiseId,
          responseId: a.ResponseId,
          isCorrect: true,
          content: '',
          contentFormat: ContentFormatType.Html,
          order: 0,
        })),
        status: questionStatus,
        statusEntity: questionStatus,
        order: question.Order,
      } as MatchingQuestion;

    case QuestionType.TrueFalse:
      // Implementation for True/False questions (similar to SingleChoice)
      return {
        id: question.Id,
        clientId:
          (question.Id || question.ClientId) ?? Guid.create().toString(),
        type: practiceLocalization.quiz,
        questionId: question.QuestionId,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi Đúng/Sai',
        content: question.Content,
        questionType: QuestionType.SingleChoice,
        options: question.Options?.map((o) => ({
          id: o.Id,
          clientId: o.Id,
          text: o.Content,
          content: o.Content,
          isCorrect: o.IsCorrect,
          contentFormat: o.ContentFormat,
          order: o.Order,
        })).sort((a, b) => a.order - b.order),
        status: questionStatus,
        statusEntity: questionStatus,
        order: question.Order,
      } as QuizQuestion;

    case QuestionType.Ordering:
      // Implementation for Ordering questions
      return {
        id: question.Id,
        clientId:
          (question.Id || question.ClientId) ?? Guid.create().toString(),
        type: practiceLocalization.matching,
        questionId: question.QuestionId,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi sắp xếp thứ tự',
        content: question.Content,
        questionType: QuestionType.Ordering,
        options: question.Options?.map((o) => ({
          id: o.Id,
          clientId: o.Id,
          text: o.Content,
          content: o.Content,
          isCorrect: o.IsCorrect,
          contentFormat: o.ContentFormat,
          order: o.Order,
        })).sort((a, b) => a.order - b.order),
        status: questionStatus,
        statusEntity: questionStatus,
        order: question.Order,
      } as any; // Replace with proper type when implemented

    default:
      throw new Error('Unsupported question type: ' + question.QuestionType);
  }
};

export interface MatchingAnswer {
  Id: string;
  QuestionId: string;
  PremiseId: string;
  ResponseId: string;
  Score?: number;
  Feedback?: string;
}

export interface MatchingItem {
  Id: string;
  QuestionId: string;
  Type: MatchingItemTypeString;
  Content: string;
  ContentFormat: string;
  Order: number;
}

export interface FillInBlankAnswer {
  Id: string;
  QuestionId: string;
  BlankIndex: number;
  CorrectAnswers: string[];
  CaseSensitive: boolean;
  Feedback?: string;
  Score?: number;
  Order: number;
}

export interface QuestionOptionDto {
  Id: string;
  Content: string;
  ContentFormat: string;
  IsCorrect: boolean;
  Order: number;
  Explanation?: string;
}

export interface QuestionDto {
  Id: string;
  QuestionId: string;
  Title?: string;
  Content: string;
  ContentFormat: string;
  QuestionType: string;
  questionType?: QuestionType;
  Difficulty: number;
  SubjectId?: string | null;
  GradeId?: string | null;
  Explanation?: string | null;
  Topics?: string;
  Tags?: string;
  Status?: number;
  Subject?: Subject;
  Grade?: LessonGrade;
  Options?: QuestionOptionDto[];
  FillInBlankAnswers: FillInBlankAnswer[];
  MatchingAnswers: MatchingAnswer[];
  MatchingItems: MatchingItem[];
  SyncQuestion?: boolean;
  LastSyncQuestionId?: string;
  ClientId?: string;
  Order?: number;
}

export interface QuestionAPIDto {
  ClientId: string;
  SectionId: string;
  QuestionId: string;
  LastSyncQuestionId: string | null;
  Order: number;
  Score: number | null;
  SyncQuestion: boolean;
  Title?: string;
  Content: string;
  ContentFormat: number;
  QuestionType: QuestionType;
  Difficulty: number;
  Status: number;
  Comment: string | null;
  SubjectId: string | null;
  GradeId: string | null;
  ShuffleOptions: boolean;
  Explanation: string;
  SourceType: number;
  Topics: string[] | null;
  Tags: string[] | null;
  Options?: QuestionOptionDto[];
  FillInBlankAnswers: FillInBlankAnswer[];
  MatchingAnswers: MatchingAnswer[];
  MatchingItems: MatchingItem[];
  Id: string;
}

export const MapQuestionBankTemplate = (
  question: QuestionAPIDto
): BaseQuestion => {
  switch (question.QuestionType) {
    case 0:
      return {
        clientId: question.ClientId ?? Guid.create().toString(),
        type: 'quiz',
        questionId: question.Id,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi nhiều đáp án',
        content: question.Content,
        questionType: QuestionType.SingleChoice,
        options: question.Options?.map((o) => ({
          id: o.Id,
          text: o.Content,
          isCorrect: o.IsCorrect,
          order: o.Order,
        })),
      } as QuizQuestion;

    case 1:
      return {
        clientId: question.ClientId ?? Guid.create().toString(),
        type: 'essay',
        questionId: question.Id,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi nhiều đáp án',
        content: question.Content,
        questionType: QuestionType.Essay,
        correctAnswer: '',
      } as EssayQuestion;
    case 2:
      return {
        clientId: question.ClientId ?? Guid.create().toString(),
        type: 'fillblanks',
        questionId: question.Id,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi nhiều đáp án',
        content: question.Content,
        questionType: QuestionType.Essay,
        options: [],
        blanks: question.FillInBlankAnswers.map((a) => ({
          id: a.Id,
          clientId: a.Id,
          correctAnswer: a.CorrectAnswers.join(', '),
          alternativeAnswers: a.CorrectAnswers,
          hint: '',
        })),
        explanation: question.Explanation,
      } as FillBlanksQuestion;
    case 3:
      return {
        clientId: question.ClientId ?? Guid.create().toString(),
        type: 'multiselect',
        questionId: question.Id,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi nhiều đáp án',
        content: question.Content,
        questionType: QuestionType.MultipleChoice,
        options: question.Options?.map((o) => ({
          id: o.Id,
          clientId: o.Id,
          text: o.Content,
          content: o.Content,
          contentFormat: ContentFormatType.Html,
          isCorrect: o.IsCorrect,
          order: o.Order,
        })),
      } as MultiSelectQuestion;
    case 4:
      return {
        questionText: '',
        clientId: question.ClientId ?? Guid.create().toString(),
        type: 'matching',
        questionId: question.Id,
        lastSyncQuestionId: question.LastSyncQuestionId,
        syncQuestion: question.SyncQuestion,
        topics: question.Topics,
        tags: question.Tags,
        subjectId: question.SubjectId,
        gradeId: question.GradeId,
        difficulty: question.Difficulty,
        title: question.Title || 'Câu hỏi nhiều đáp án',
        content: question.Content,
        questionType: QuestionType.Matching,
        matchingItems: question.MatchingItems.map((i) => ({
          id: i.Id,
          clientId: i.Id,
          content: i.Content,
          type:
            i.Type === MatchingItemTypeString.Premise
              ? MatchingItemType.Premise
              : MatchingItemType.Response,
          order: 0,
        })),
        matchingAnswers: question.MatchingAnswers.map((a) => ({
          id: a.Id,
          clientId: a.Id,
          content: '',
          contentFormat: ContentFormatType.Html,
          premiseId: a.PremiseId,
          responseId: a.ResponseId,
          isCorrect: true,
          order: 0,
        })),
      } as MatchingQuestion;

    default:
      throw new Error('Unsupported question type: ' + question.QuestionType);
  }
};
