//#region DOCUMENTATION
/**
 * QuizComponent - Một Component câu hỏi trắc nghiệm hỗ trợ cả chế độ hiển thị và cấu hình
 *
 * @component
 * @description
 * Một React component hiển thị câu hỏi trắc nghiệm với nhiều lựa chọn. Hỗ trợ:
 * - Chế độ hiển thị cho người dùng trả lời câu hỏi
 * - Chế độ cấu hình để tạo/chỉnh sửa câu hỏi
 * - Chỉnh sửa nội dung câu hỏi với rich text
 * - Các lựa chọn trả lời trắc nghiệm
 * - Phản hồi ngay lập tức sau khi nộp bài
 * - Hiển thị giải thích cho câu trả lời
 *
 * @example
 * ```tsx
 * <QuizComponent
 *   question={quizQuestion}
 *   onComplete={handleComplete}
 *   showFeedback={true}
 *   configMode={false}
 * />
 * ```
 *
 * @param {QuizComponentProps} props - <PERSON><PERSON><PERSON> thu<PERSON> tính của component
 * @param {QuizQuestion} props.question - Đối tượng câu hỏi chứa tiêu đề, nội dung và các lựa chọn
 * @param {Function} [props.onComplete] - Hàm callback khi người dùng hoàn thành bài quiz
 * @param {boolean} [props.showFeedback=true] - Có hiển thị phản hồi sau khi nộp bài hay không
 * @param {boolean} [props.hideSaveButton=false] - Có ẩn nút lưu trong chế độ cấu hình hay không
 * @param {boolean} [props.configMode=false] - Có hiển thị component ở chế độ cấu hình hay không
 * @param {boolean} [props.allowManyTimes=false] - Có cho phép làm lại nhiều lần hay không
 * @param {boolean} [props.disabled=false] - Có vô hiệu hóa component hay không
 * @param {boolean} [props.showResult=false] - Có hiển thị kết quả hay không
 * @param {Object} [props.htmlAttributes] - Các thuộc tính HTML bổ sung cho component Card
 *
 * @returns {JSX.Element} Một component quiz với giao diện hiển thị hoặc cấu hình
 */
//#endregion

import './quizComponent.css';
import React, {
  // lazy,
  memo,
  useCallback,
  useContext,
  useEffect,
  useState,
  useMemo,
} from 'react';
import {
  Card,
  Radio,
  Button,
  Space,
  Input,
  Form,
  PopconfirmProps,
  Row,
  Col,
} from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  PlusOutlined,
  DeleteFilled,
  UndoOutlined,
  RedoOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  MenuOutlined,
} from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  BaseQuestion,
  EngineQuestionComponent,
  PracticeEngineContext,
  QuizAnswer,
  QuizQuestion,
} from '../../../interfaces/quizs/questionBase';
import './QuizAnimations.css';
import { QuizComponentProps } from '../../../interfaces/quizs/quizComponent.interface';
import { quizLocalization } from '../localization';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import { Guid } from 'guid-typescript';
import { MathContent } from '../../common/MathJax/MathJaxWrapper';
import LoadingScreen from '../../common/Loading/LoadingScreen';

import '../../common/elements/Text/RichTextEditorComponent/v0/RichTextEditorStyles.css';
import CustomRichText from '../../common/elements/Text/RichTextEditorComponent/v0/CustomRichText';
import BaseQuestionPreviewComponent from '../base/BaseQuestionPreviewComponent';
import { ContentFormatType } from '../../../interfaces/exams/examEnums';
import useDebouncedCallback from '../../../hooks/apps/useDebouncedCallback';
import { quizCheckCorrectAnswer } from './quizUtils';

const { TextArea } = Input;

const QuizComponent: EngineQuestionComponent<QuizComponentProps> = ({
  question,
  onComplete,
  configMode = false,
  allowManyTimes: _allowManyTimes = false,
  disabled = false,
  showResult = false,
  htmlAttributes,
  options = {
    hideDeleteButton: false,
    hideSaveButton: false,
    hideFeedback: false,
  },
  isMarked = false,
  onToggleMark,
  questionIndex,
}: QuizComponentProps) => {
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { handlePendingState } = useContext(PracticeEngineContext);

  const { isPending } = useUpdateQuestion();
  useEffect(() => {
    if (handlePendingState) handlePendingState(isPending);
  }, [isPending]);

  const [form] = Form.useForm();

  const isAnswered = useMemo(() => {
    return !!selectedAnswer;
  }, [selectedAnswer]);

  const isCorrect = useMemo(() => {
    if (!showResult || !isAnswered) return false;
    const selectedOption = question.options?.find(
      (o) => o.clientId === selectedAnswer
    );
    if (!selectedOption) return false;
    const checkResult = quizCheckCorrectAnswer(question, selectedOption);
    return checkResult === true;
  }, [showResult, isAnswered, question, selectedAnswer]);

  // Handle answer selection
  const handleAnswerSelect = useCallback(
    (answerId: string) => {
      if (!isSubmitted) {
        setSelectedAnswer(answerId);
        // Store answer without auto-submitting
        const selectedOption = question.options?.find(
          (answer) => answer.clientId === answerId
        );
        if (onComplete) {
          onComplete(question.clientId, selectedOption);
        }
      }
    },
    [isSubmitted, question.options, question.clientId, onComplete]
  );

  // Reset the question
  const handleReset = () => {
    setSelectedAnswer(question.userSelect?.clientId ?? null);
    setIsSubmitted(false);

    if (configMode) {
      form.setFieldsValue({
        title: question.title,
        content: question.content,
        points: question.points || 1,
        explanation: question.explanation || '',
        options:
          question.options?.map((option) => ({
            ...option,
            key: option.clientId,
            isCorrect: option.isCorrect,
          })) ?? [],
      });
    }
  };

  // Get class name for answer option
  const getAnswerClassName = (answer: QuizAnswer) => {
    // Không hiển thị class nếu không ở chế độ kết quả hoặc chưa submit
    if (!isSubmitted && !showResult) return '';

    // Nếu đây là đáp án được chọn
    if (answer.clientId === selectedAnswer) {
      return answer.isCorrect
        ? 'correct-selected-answer'
        : 'incorrect-selected-answer';
    }

    // Nếu đây là đáp án đúng (nhưng không được chọn)
    if (answer.isCorrect) {
      return 'correct-unselected-answer';
    }

    return '';
  };

  // Get inline styles for answer option
  const getAnswerStyle = (answer: QuizAnswer) => {
    // Không áp dụng style nếu không ở chế độ kết quả hoặc chưa submit
    if (!isSubmitted && !showResult) return {};

    // Nếu đây là đáp án được chọn
    if (answer.clientId === selectedAnswer) {
      return answer.isCorrect
        ? { backgroundColor: '#f6ffed', borderColor: '#b7eb8f' } // Đáp án đúng được chọn - màu xanh
        : { backgroundColor: '#fff2f0', borderColor: '#ffccc7' }; // Đáp án sai được chọn - màu đỏ
    }

    // Nếu đây là đáp án đúng nhưng không được chọn - giữ màu mặc định
    return {};
  };

  useEffect(() => {
    handleReset();
  }, [question.clientId, question.userSelect]);

  // Render content for BaseQuestionPreviewComponent
  const renderContent = () => {
    return (
      <div className="quiz-question item-config">
        <div
          style={{
            color: '#1a1a1a',
          }}
        >
          <MathContent html={question.content} />
        </div>
      </div>
    );
  };

  // Render interaction for BaseQuestionPreviewComponent
  const renderInteraction = () => {
    return (
      <>
        {configMode && (
          <div className="quiz-toolbar" style={{ marginBottom: '16px' }}>
            <Space>
              <Button icon={<UndoOutlined />} />
              <Button icon={<RedoOutlined />} />
              <div
                style={{
                  width: '1px',
                  background: '#d9d9d9',
                  height: '24px',
                }}
              />
              <Button icon={<BoldOutlined />} />
              <Button icon={<ItalicOutlined />} />
              <Button icon={<UnderlineOutlined />} />
              <div
                style={{
                  width: '1px',
                  background: '#d9d9d9',
                  height: '24px',
                }}
              />
              <Button icon={<OrderedListOutlined />} />
              <Button icon={<UnorderedListOutlined />} />
            </Space>
          </div>
        )}

        <div className="quiz-options item-config">
          <Radio.Group
            value={selectedAnswer}
            onChange={(e) => handleAnswerSelect(e.target.value)}
            style={{ width: '100%' }}
            disabled={showResult || isSubmitted || disabled}
          >
            <Row gutter={[16, 16]}>
              {question.options
                ?.slice()
                ?.sort((a, b) => a.order - b.order)
                ?.map((answer, index) => (
                  <Col
                    span={12}
                    key={`col-${answer.clientId || answer.id || index}`}
                  >
                    <Radio
                      key={`radio-${answer.clientId || answer.id || index}`}
                      value={answer.clientId}
                      className={`quiz-option ${getAnswerClassName(answer)}`}
                      style={{
                        width: '100%',
                        height: '100%',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid #d9d9d9',
                        transition: 'all 0.3s',
                        display: 'flex',
                        alignItems: 'flex-start',
                        ...getAnswerStyle(answer),
                      }}
                    >
                      <div
                        key={`option-content-${answer.clientId}`}
                        style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          width: '100%',
                          position: 'relative',
                          flex: 1,
                          minHeight: '1.5em',
                        }}
                      >
                        <div
                          style={{ flex: 1, minWidth: 0, paddingRight: '24px' }}
                        >
                          <MathContent
                            html={`${String.fromCharCode(65 + index)}. ${
                              answer.content
                            }`}
                          />
                        </div>
                        {(isSubmitted || showResult) && (
                          <div
                            key={`option-icon-${answer.clientId}`}
                            style={{
                              position: 'absolute',
                              right: '0',
                              top: '0',
                              display: 'flex',
                              alignItems: 'center',
                              height: '1.5em',
                            }}
                          >
                            {answer.isCorrect ? (
                              // Đáp án đúng luôn hiển thị tích xanh
                              <CheckOutlined
                                style={{ color: '#52c41a', fontSize: '14px' }}
                              />
                            ) : (
                              // Đáp án sai chỉ hiển thị X đỏ khi được chọn
                              answer.clientId === selectedAnswer && (
                                <CloseOutlined
                                  style={{ color: '#ff4d4f', fontSize: '14px' }}
                                />
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </Radio>
                  </Col>
                ))}
            </Row>
          </Radio.Group>
        </div>
      </>
    );
  };

  if (configMode) {
    return (
      <Card
        {...htmlAttributes}
        className={'quiz-component ' + htmlAttributes?.className}
      >
        <QuizComponent.ConfigMode
          question={question}
          disabled={disabled}
          options={options}
        />
      </Card>
    );
  }

  return (
    <BaseQuestionPreviewComponent
      question={question}
      questionIndex={questionIndex}
      isCorrect={isCorrect}
      hideFeedback={options.hideFeedback}
      showResult={showResult}
      isAnswered={isAnswered}
      cardClassName="quiz-card"
      guidanceText="Chọn một đáp án đúng."
      renderContent={renderContent}
      renderInteraction={renderInteraction}
      isMarked={isMarked}
      onToggleMark={onToggleMark}
    />
  );
};

// Sortable Option Item Component for drag and drop
interface SortableOptionItemProps {
  option: any;
  index: number;
  field: any;
  onCorrectAnswerChange: (id: string) => void;
  onDelete: (id: string) => void;
  disabled: boolean;
  minOptionsCount: number;
}

const SortableOptionItem: React.FC<SortableOptionItemProps> = ({
  option,
  index,
  field,
  onCorrectAnswerChange,
  onDelete,
  disabled,
  minOptionsCount,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: option.clientId });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: isDragging ? '#f6ffed' : 'transparent',
    borderColor: isDragging ? '#b7eb8f' : 'transparent',
    display: 'flex',
    alignItems: 'center',
    marginBottom: '8px',
    gap: '8px',
    padding: '8px',
    border: '1px solid transparent',
    borderRadius: '6px',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="option-item tailwind-shadow-md"
    >
      <div
        {...attributes}
        {...listeners}
        className="tailwind-cursor-grab tailwind-text-gray-400 hover:tailwind-text-gray-700"
        style={{ padding: '4px' }}
      >
        <MenuOutlined />
      </div>

      <Form.Item
        {...field}
        name={[field.name, 'isCorrect']}
        valuePropName="checked"
        noStyle
        className="multi-select-option-checkbox"
      >
        <Radio
          checked={option.isCorrect}
          onChange={() => onCorrectAnswerChange(option.clientId)}
          style={{ transform: 'scale(1.25)' }}
        />
      </Form.Item>

      <div className="tailwind-flex tailwind-items-center tailwind-w-full tailwind-ml-3">
        <span className="option-label" style={{ marginRight: '8px' }}>
          {String.fromCharCode(65 + index)}.
        </span>
        <Form.Item
          {...field}
          name={[field.name, 'content']}
          rules={[
            {
              required: true,
              message: 'Nội dung không được để trống!',
            },
          ]}
          noStyle
          className="multi-select-option-input tailwind-flex"
        >
          <Input
            size="middle"
            variant="underlined"
            style={{ flex: 1 }}
            placeholder={`Tùy chọn ${index + 1}`}
          />
        </Form.Item>
      </div>

      <Button
        danger
        icon={<DeleteOutlined />}
        onClick={() => onDelete(option.clientId)}
        disabled={disabled || minOptionsCount <= 2}
      />
    </div>
  );
};

interface ConfigModeProps {
  question: QuizQuestion;
  disabled?: boolean;
  options?: {
    hideDeleteButton?: boolean;
    hideSaveButton?: boolean;
    hideFeedback?: boolean;
  };
}

// Sub-component for ConfigMode

QuizComponent.ConfigMode = ({
  question,
  disabled = false,
  options = {
    hideDeleteButton: false,
    hideSaveButton: false,
    hideFeedback: false,
  },
}: ConfigModeProps) => {
  // const [focus, setFocus] = useState<string | null>(null);
  const [questionTextProps, setQuestionTextProps] = useState<string>(
    () => question.content
  );

  // Add state for explanation text
  const [explanationTextProps, setExplanationTextProps] = useState<string>(
    () => question.explanation ?? ''
  );

  // State for sortable options
  const [sortableOptions, setSortableOptions] = useState<any[]>(() => {
    return (
      question.options?.map((opt) => ({
        ...opt,
        key: opt.clientId,
      })) || []
    );
  });

  const [form] = Form.useForm();
  const {
    handleDeleteQuestion: externalHandleDeleteQuestion,
    handlePendingState,
  } = useContext(PracticeEngineContext);

  const { handleUpdateQuestion, isPending } = useUpdateQuestion();

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sortableOptions.findIndex(
        (option) => option.clientId === active.id
      );
      const newIndex = sortableOptions.findIndex(
        (option) => option.clientId === over.id
      );

      if (oldIndex !== -1 && newIndex !== -1) {
        const newOptions = arrayMove(sortableOptions, oldIndex, newIndex);

        // Update order property
        const updatedOptions = newOptions.map((opt, index) => ({
          ...opt,
          order: index,
        }));

        setSortableOptions(updatedOptions);

        // Update form
        form.setFieldsValue({
          options: updatedOptions,
        });

        // Update question
        const updatedQuestion: QuizQuestion = {
          ...question,
          options: updatedOptions,
        };
        handleUpdateQuestion(updatedQuestion as BaseQuestion);
      }
    }
  };

  useEffect(() => {
    if (handlePendingState) handlePendingState(isPending);
  }, [isPending, handlePendingState]);

  // Reset the form with question data
  useEffect(() => {
    const formOptions =
      question.options?.map((option) => ({
        ...option,
        key: option.clientId,
        isCorrect: option.isCorrect,
      })) ?? [];

    form.setFieldsValue({
      title: question.title,
      content: question.content,
      points: question.points || 1,
      explanation: question.explanation || '',
      options: formOptions,
    });

    setSortableOptions(formOptions);
    setQuestionTextProps(question.content ?? '');
    setExplanationTextProps(question.explanation ?? '');
  }, [question, form]);

  const handleQuestionTextChange = useDebouncedCallback(
    (updates?: string) => {
      if (disabled) return;
      if (updates) {
        form.setFieldsValue({
          content: updates,
          description: updates,
        });
        const updatedQuestion: QuizQuestion = {
          ...question,
          clientId: question.clientId,
          parentId: question.parentId,
          content: updates,
          description: updates,
        };
        handleUpdateQuestion(updatedQuestion as BaseQuestion);

        setQuestionTextProps(updates);
      }
    },
    [question, handleUpdateQuestion, disabled]
  );

  const handleCorrectAnswerChange = useCallback(
    (id: string) => {
      const newAnswers =
        question.options?.map((answer) => ({
          ...answer,
          isCorrect: answer.clientId === id,
        })) ?? [];
      form.setFieldsValue({
        options: newAnswers,
      });

      const updatedQuestion: QuizQuestion = {
        ...question,
        clientId: question.clientId,
        parentId: question.parentId,
        options: newAnswers,
      };
      handleUpdateQuestion(updatedQuestion as BaseQuestion);
    },
    [question, handleUpdateQuestion]
  );

  const addNewOption = () => {
    const options = form.getFieldValue('options') || [];
    const newId = Guid.create().toString();
    const newOption = {
      key: newId,
      clientId: newId,
      content: 'Tùy chọn mới',
      contentFormat: ContentFormatType.Html,
      isCorrect: false,
      order: options.length,
    };

    const updatedOptions = [...options, newOption];

    form.setFieldsValue({
      options: updatedOptions,
    });

    setSortableOptions(updatedOptions);

    handleUpdateQuestion({
      ...question,
      options: updatedOptions,
      clientId: question.clientId,
    });
  };

  const deleteOption = (id: string) => {
    if (disabled || (question.options?.length ?? 0) <= 2) return;
    const newAnswers =
      question.options?.filter((answer) => answer.clientId !== id) ?? [];

    const formAnswers = newAnswers.map((answer) => ({
      ...answer,
      key: answer.clientId,
    }));

    form.setFieldsValue({
      options: formAnswers,
    });

    setSortableOptions(formAnswers);

    const newQuestion = { ...question, options: newAnswers } as QuizQuestion;
    handleUpdateQuestion(newQuestion);
  };

  // Handle save configuration
  const handleSaveConfig = () => {
    if (handleUpdateQuestion) {
      handleUpdateQuestion(question);
    }
  };

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.clientId);
  };

  const handleFormChange = useDebouncedCallback(
    (_changedValues: any, values: any) => {
      const updatedQuestion: QuizQuestion = {
        ...question,
        title: values.title,
        content: values.content,
        description: values.content,
        points: values.points,
        explanation: values.explanation,
        options:
          values.options?.map((opt: any, index: number) => ({
            ...opt,
            order: index,
            clientId: opt.clientId || opt.key,
          })) || question.options,
      };
      handleUpdateQuestion(updatedQuestion as BaseQuestion);
    },
    [question, handleUpdateQuestion]
  );

  const handleExplanationTextChange = useCallback(
    (updates?: string) => {
      if (disabled) return;
      if (updates !== undefined) {
        form.setFieldsValue({
          explanation: updates,
        });
        handleFormChange(
          {
            explanation: updates,
          },
          form.getFieldsValue()
        );
      }
    },
    [question, disabled, handleFormChange]
  );

  return (
    <Form
      className="quiz-component-form"
      layout="vertical"
      form={form}
      onValuesChange={handleFormChange}
      initialValues={{
        title: question.title,
        content: question.content,
        points: question.points || 1,
        explanation: question.explanation || '',
        options:
          question.options?.map((opt) => ({
            ...opt,
            key: opt.clientId,
          })) || [],
      }}
    >
      <Form.Item required name="title" hidden>
        <Input
          variant="underlined"
          placeholder={quizLocalization.form.questionTitle.placeholder}
          className="tailwind-font-medium tailwind-text-lg"
        />
      </Form.Item>

      <Form.Item required name="content" label="Nội dung">
        <React.Suspense
          fallback={
            <div className="tailwind-h-200 tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-center">
              <LoadingScreen />
            </div>
          }
        >
          <CustomRichText
            id={`quiz-question-${question.clientId}`}
            value={questionTextProps}
            height={200}
            onChangeValue={(_, value) => {
              if (value !== undefined) {
                handleQuestionTextChange(value);
              }
            }}
            placeholder={quizLocalization.form.questionText.placeholder}
            disabled={disabled}
            toolbarSettingItems={[
              'Bold',
              'Italic',
              'Underline',
              'OrderedList',
              'UnorderedList',
              'Image',
              'CreateLink',
            ]}
          />
        </React.Suspense>
      </Form.Item>

      <Form.Item name="options" label="Các tuỳ chọn đáp án" required>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <Form.List name="options">
            {(fields, { add: _add, remove: _remove }) => {
              return (
                <SortableContext
                  items={sortableOptions.map((option) => option.clientId)}
                  strategy={verticalListSortingStrategy}
                >
                  <Space
                    direction="vertical"
                    style={{ width: '100%' }}
                    className="multi-select-options-list"
                  >
                    {fields.map((field, index) => {
                      const option = form.getFieldValue([
                        'options',
                        field.name,
                      ]);
                      return (
                        <SortableOptionItem
                          key={option.clientId}
                          option={option}
                          index={index}
                          field={field}
                          onCorrectAnswerChange={handleCorrectAnswerChange}
                          onDelete={deleteOption}
                          disabled={disabled}
                          minOptionsCount={question.options?.length ?? 0}
                        />
                      );
                    })}
                  </Space>
                </SortableContext>
              );
            }}
          </Form.List>
        </DndContext>
        <Button
          type="dashed"
          onClick={addNewOption}
          icon={<PlusOutlined />}
          style={{ width: '100%', marginTop: '8px' }}
          disabled={disabled}
        >
          {quizLocalization.form.options.addOption}
        </Button>
      </Form.Item>
      <Form.Item
        name="explanation"
        label={quizLocalization.form.explanation.label}
      >
        <React.Suspense
          fallback={
            <div className="tailwind-h-200 tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-center">
              <LoadingScreen />
            </div>
          }
        >
          <CustomRichText
            id={`quiz-explanation-${question.clientId}`}
            value={form.getFieldValue('explanation')}
            height={150}
            onChangeValue={(_, value) => {
              if (value !== undefined) {
                handleExplanationTextChange(value);
              }
            }}
            placeholder={quizLocalization.form.explanation.placeholder}
            disabled={disabled}
            toolbarSettingItems={[
              'Bold',
              'Italic',
              'Underline',
              'OrderedList',
              'UnorderedList',
              'Image',
              'CreateLink',
            ]}
          />
        </React.Suspense>
      </Form.Item>
      <Form.Item>
        <Space>
          {!options.hideSaveButton && (
            <Button type="primary" onClick={handleSaveConfig}>
              {quizLocalization.buttons.saveChanges}
            </Button>
          )}
          {!options.hideDeleteButton && (
            <PopconfirmAntdCustom
              title={quizLocalization.buttons.deleteQuestion.confirmTitle}
              onConfirm={deleteConfirm}
              onCancel={() => {}}
              okText={quizLocalization.buttons.deleteQuestion.yes}
              cancelText={quizLocalization.buttons.deleteQuestion.no}
            >
              <Button danger icon={<DeleteFilled />} disabled={false}>
                {quizLocalization.buttons.deleteQuestion.button}
              </Button>
            </PopconfirmAntdCustom>
          )}
        </Space>
      </Form.Item>
    </Form>
  );
};

QuizComponent.PreviewMode = () => {
  return <div>PreviewMode</div>;
};

export default memo(QuizComponent);
