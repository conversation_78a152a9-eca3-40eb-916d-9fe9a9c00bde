﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/client-proxies/edtect-study-proxy.3z86kgqw0j.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\client-proxies\edtect-study-proxy.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3z86kgqw0j"},{"Name":"integrity","Value":"sha256-ERkF\u002BiNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU="},{"Name":"label","Value":"_content/EdTech.Study.Web/client-proxies/edtect-study-proxy.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6805"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ERkF\u002BiNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 09:17:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/client-proxies/edtect-study-proxy.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\client-proxies\edtect-study-proxy.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ERkF\u002BiNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6805"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ERkF\u002BiNRHoWCgQE/DSqSa7sZBhEyie5rnw17N1pbCGU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 09:17:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/css/home/<USER>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\home\layout.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hy141e0Vb2Xqf\u002BqlOv3gGM2i37kSLzEyQAtg46wXMQg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3648"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hy141e0Vb2Xqf\u002BqlOv3gGM2i37kSLzEyQAtg46wXMQg=\u0022"},{"Name":"Last-Modified","Value":"Fri, 30 May 2025 08:20:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/css/home/<USER>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\home\layout.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tf83deabgg"},{"Name":"integrity","Value":"sha256-hy141e0Vb2Xqf\u002BqlOv3gGM2i37kSLzEyQAtg46wXMQg="},{"Name":"label","Value":"_content/EdTech.Study.Web/css/home/<USER>"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3648"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hy141e0Vb2Xqf\u002BqlOv3gGM2i37kSLzEyQAtg46wXMQg=\u0022"},{"Name":"Last-Modified","Value":"Fri, 30 May 2025 08:20:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/css/lesson-config-custom.bhnzboqiya.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\lesson-config-custom.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bhnzboqiya"},{"Name":"integrity","Value":"sha256-YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs="},{"Name":"label","Value":"_content/EdTech.Study.Web/css/lesson-config-custom.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2671"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 09:17:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/css/lesson-config-custom.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\lesson-config-custom.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2671"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YTOb31O4972ojGcyZOrrDLEb7HNU9nKfLOGmTjVLFJs=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 09:17:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/css/lesson-config.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\lesson-config.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LGcgCqx/2ub6z2TJpYO2m\u002BjpiKWOLqpFR7RrECeAig4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1024"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022LGcgCqx/2ub6z2TJpYO2m\u002BjpiKWOLqpFR7RrECeAig4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 09:17:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/css/lesson-config.cuxmsiayrj.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\lesson-config.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cuxmsiayrj"},{"Name":"integrity","Value":"sha256-LGcgCqx/2ub6z2TJpYO2m\u002BjpiKWOLqpFR7RrECeAig4="},{"Name":"label","Value":"_content/EdTech.Study.Web/css/lesson-config.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1024"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022LGcgCqx/2ub6z2TJpYO2m\u002BjpiKWOLqpFR7RrECeAig4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 09:17:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/175.4spbtdzkua.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\175.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4spbtdzkua"},{"Name":"integrity","Value":"sha256-vOcjBxkvmhLSqS\u002BPXCfHU1D7MhoTpxRg2eOMfdUmqxQ="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/175.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"219827"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022vOcjBxkvmhLSqS\u002BPXCfHU1D7MhoTpxRg2eOMfdUmqxQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/175.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\175.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vOcjBxkvmhLSqS\u002BPXCfHU1D7MhoTpxRg2eOMfdUmqxQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"219827"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022vOcjBxkvmhLSqS\u002BPXCfHU1D7MhoTpxRg2eOMfdUmqxQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/175.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\175.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb\u002BJX0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"562177"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb\u002BJX0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/175.js.mrzq9y32w4.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\175.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mrzq9y32w4"},{"Name":"integrity","Value":"sha256-bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb\u002BJX0="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/175.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"562177"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022bqeShQhf19Kq6h6n9Z9V7Hkd4oL4b8hgP1Kjheb\u002BJX0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/185.23t9yabonc.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\185.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"23t9yabonc"},{"Name":"integrity","Value":"sha256-fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/185.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3436517"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/185.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\185.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3436517"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022fnVbWYQEcwVSLEnms1EN31CyTAEdz2kq51zLdF9HCb8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/185.js.LICENSE.95gyj95t05.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\185.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"95gyj95t05"},{"Name":"integrity","Value":"sha256-wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/185.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"34299"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/185.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\185.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"34299"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022wzIUBtMa/bKanR6YXtjA8fegHam3UH3wbeSL3L/PEIg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/185.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\185.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-N2Glc7c741k\u002B36euUNplrCMbo/wwh9z74OrYNWAKXa0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11873346"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022N2Glc7c741k\u002B36euUNplrCMbo/wwh9z74OrYNWAKXa0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/185.js.n8g4689brp.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\185.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n8g4689brp"},{"Name":"integrity","Value":"sha256-N2Glc7c741k\u002B36euUNplrCMbo/wwh9z74OrYNWAKXa0="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/185.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11873346"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022N2Glc7c741k\u002B36euUNplrCMbo/wwh9z74OrYNWAKXa0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/525.dvrkq7h6at.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\525.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dvrkq7h6at"},{"Name":"integrity","Value":"sha256-58844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/525.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"423393"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002258844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/525.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\525.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-58844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"423393"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002258844JHURtb23BcxYDtJUKTdrchwdTGKL4GeuTGBLXI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/525.js.LICENSE.0f2hyzvqhq.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\525.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0f2hyzvqhq"},{"Name":"integrity","Value":"sha256-fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/525.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23237"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/525.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\525.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23237"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022fGSm5/O0iWJrIbfv7LbLd5FG6g/sYDvJrAWhc6Fhq34=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/525.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\525.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1824392"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/525.js.x2mwtu8ust.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\525.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x2mwtu8ust"},{"Name":"integrity","Value":"sha256-yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/525.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1824392"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022yazBjSl97zIXzTSCDg9SQQyjYGnAHD395qz19qltnjI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/652.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\652.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/NIAn4qcOl8VJTos\u002BJmrc85kuLbbY1jaZ0GuFAkOfgw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"12571"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/NIAn4qcOl8VJTos\u002BJmrc85kuLbbY1jaZ0GuFAkOfgw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/652.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\652.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3151"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/652.js.LICENSE.ynwpht6pq8.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\652.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ynwpht6pq8"},{"Name":"integrity","Value":"sha256-nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/652.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3151"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022nlbTmjp8Z61rTVWo1vncWHEYGsrsV3oWbaUzWUvsJHQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/652.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\652.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"44904"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/652.js.ouwzrm6kt8.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\652.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ouwzrm6kt8"},{"Name":"integrity","Value":"sha256-EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/652.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"44904"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022EASMdzGe/0g/cGBadtrCiS1cITZIWzc39Ubc5vbQgks=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/652.o5vpl5kna0.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\652.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o5vpl5kna0"},{"Name":"integrity","Value":"sha256-/NIAn4qcOl8VJTos\u002BJmrc85kuLbbY1jaZ0GuFAkOfgw="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/652.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12571"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/NIAn4qcOl8VJTos\u002BJmrc85kuLbbY1jaZ0GuFAkOfgw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/915.5jzajkzt9s.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\915.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5jzajkzt9s"},{"Name":"integrity","Value":"sha256-YQVZ6NrdDd99tqjxhQNTBuiJD1x\u002B8hiW6LyHxFSLvx8="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/915.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16112"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YQVZ6NrdDd99tqjxhQNTBuiJD1x\u002B8hiW6LyHxFSLvx8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/915.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\915.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YQVZ6NrdDd99tqjxhQNTBuiJD1x\u002B8hiW6LyHxFSLvx8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16112"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YQVZ6NrdDd99tqjxhQNTBuiJD1x\u002B8hiW6LyHxFSLvx8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/915.js.cltsnaqjg5.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\915.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cltsnaqjg5"},{"Name":"integrity","Value":"sha256-0MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/915.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"55644"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00220MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/915.js.LICENSE.ndl5lth528.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\915.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ndl5lth528"},{"Name":"integrity","Value":"sha256-exSM61kpL\u002BQ2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/915.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"602"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022exSM61kpL\u002BQ2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/915.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\915.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-exSM61kpL\u002BQ2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"602"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022exSM61kpL\u002BQ2O3O27mB06xnKMi2SNhSBcgeLUTHMgHk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/915.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\915.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"55644"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00220MNmlBJAQqgSfxrWkvHTUZN5SikXI51Xg4yZEeQMro8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/app.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\app.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-W\u002Bc18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6\u002BSpStPY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"247592"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022W\u002Bc18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6\u002BSpStPY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/app.css.3m39eyus15.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\app.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3m39eyus15"},{"Name":"integrity","Value":"sha256-dQ\u002BRUmPNFWrSQfmdVg3x2HBismw5\u002B9QdxLr7k7aLjgk="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/css/app.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"289495"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022dQ\u002BRUmPNFWrSQfmdVg3x2HBismw5\u002B9QdxLr7k7aLjgk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/app.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\app.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dQ\u002BRUmPNFWrSQfmdVg3x2HBismw5\u002B9QdxLr7k7aLjgk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"289495"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022dQ\u002BRUmPNFWrSQfmdVg3x2HBismw5\u002B9QdxLr7k7aLjgk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/app.dqnaqurh47.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\app.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dqnaqurh47"},{"Name":"integrity","Value":"sha256-W\u002Bc18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6\u002BSpStPY="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/css/app.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"247592"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022W\u002Bc18YlapjLJoG9aPSHoAjhzLmqW5zR4mLK6\u002BSpStPY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/vendor.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\vendor.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Uc17aF4\u002BllpQWTbh9hhovepW/T\u002BylCwe5KiagG6YX7s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2211065"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Uc17aF4\u002BllpQWTbh9hhovepW/T\u002BylCwe5KiagG6YX7s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/vendor.css.0oam4xxl3a.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\vendor.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0oam4xxl3a"},{"Name":"integrity","Value":"sha256-gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/css/vendor.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2533424"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/vendor.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\vendor.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2533424"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022gChj3TZ10NrsznKyXyVn273FlUjJhMVQc6Qs5arZjFY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/css/vendor.t1ddakriei.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\css\vendor.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t1ddakriei"},{"Name":"integrity","Value":"sha256-Uc17aF4\u002BllpQWTbh9hhovepW/T\u002BylCwe5KiagG6YX7s="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/css/vendor.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2211065"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Uc17aF4\u002BllpQWTbh9hhovepW/T\u002BylCwe5KiagG6YX7s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/CoordinateFinderGame.ig4295qi4k.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\CoordinateFinderGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ig4295qi4k"},{"Name":"integrity","Value":"sha256-EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1\u002BDTPg="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/CoordinateFinderGame.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"315838"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1\u002BDTPg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/CoordinateFinderGame.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\CoordinateFinderGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1\u002BDTPg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"315838"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EoDJg2zp2DVqHN1E55pR4vQudxfY0xvrB5/6b1\u002BDTPg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Demo.fafyiyrk0h.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Demo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fafyiyrk0h"},{"Name":"integrity","Value":"sha256-GbS4fQvzM0f\u002BPD96WCHkSqP57LvAf4mf2at7\u002Bw6pMf8="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Demo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"769743"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022GbS4fQvzM0f\u002BPD96WCHkSqP57LvAf4mf2at7\u002Bw6pMf8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Demo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Demo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GbS4fQvzM0f\u002BPD96WCHkSqP57LvAf4mf2at7\u002Bw6pMf8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"769743"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022GbS4fQvzM0f\u002BPD96WCHkSqP57LvAf4mf2at7\u002Bw6pMf8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Game.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Game.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EezFI1A4HD51SZGlPcwJpvti\u002B6BSBT6y6a4AAW/cOhM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"212262"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EezFI1A4HD51SZGlPcwJpvti\u002B6BSBT6y6a4AAW/cOhM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Game.x8px27nr1p.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Game.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x8px27nr1p"},{"Name":"integrity","Value":"sha256-EezFI1A4HD51SZGlPcwJpvti\u002B6BSBT6y6a4AAW/cOhM="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Game.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"212262"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EezFI1A4HD51SZGlPcwJpvti\u002B6BSBT6y6a4AAW/cOhM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/layers-2x.ef5uc09gvw.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\layers-2x.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ef5uc09gvw"},{"Name":"integrity","Value":"sha256-Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/layers-2x.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1259"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/layers-2x.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\layers-2x.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1259"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Bm2sqFDY/77wB68AsG6sABVyje4nnFHzy2xxbffELt8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/layers.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\layers.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"696"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/layers.ptvguihtoq.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\layers.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ptvguihtoq"},{"Name":"integrity","Value":"sha256-Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/layers.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"696"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Hbvp0CjikvNvy6j4s6KNXokydU/CIVuaxp5M3s9RB8Y=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Layout.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Layout.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3\u002BBDbM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"722546"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00220xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3\u002BBDbM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Layout.vl8hl453qh.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Layout.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vl8hl453qh"},{"Name":"integrity","Value":"sha256-0xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3\u002BBDbM="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Layout.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"722546"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00220xpVrSsRashMKU3PygClc9FKiFsB8teAxGbUg3\u002BBDbM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/marker-icon.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\marker-icon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1466"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/marker-icon.vq9wvlav5t.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\marker-icon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vq9wvlav5t"},{"Name":"integrity","Value":"sha256-V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/marker-icon.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1466"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022V0w6XMqF9BFAhbaEFZbWLwDXyJLHsD8oy/owHesdxDc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/marker-shadow.enp07i83pd.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\marker-shadow.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"enp07i83pd"},{"Name":"integrity","Value":"sha256-Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/marker-shadow.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"618"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/marker-shadow.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\marker-shadow.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"618"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Jk9cZAM58ELdcpBiz8BMF/jqDymIK1OOOEjtjxDttNo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/MillionaireGame.bmremb8khj.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\MillionaireGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bmremb8khj"},{"Name":"integrity","Value":"sha256-rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/MillionaireGame.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"183086"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/MillionaireGame.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\MillionaireGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"183086"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022rRpKtpOSt3r4h5r0efJgb77d3lQzDD3y3hyh/0Bmdgc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/practiceBackground.lxs3uvnp0c.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\practiceBackground.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lxs3uvnp0c"},{"Name":"integrity","Value":"sha256-x\u002BBj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/practiceBackground.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"439075"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022x\u002BBj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/practiceBackground.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\practiceBackground.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-x\u002BBj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"439075"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022x\u002BBj2kMKHrvUqi4uOXSxpAZN2xVghaenXsjNOzvaw18=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Quiz.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Quiz.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"202771"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Quiz.yw6d7ci7jx.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Quiz.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yw6d7ci7jx"},{"Name":"integrity","Value":"sha256-Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Quiz.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"202771"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Qlt2AfxYRdlKSOzxXEBUt7qLORfLuUqgthm6NFmEPKA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/QuizCardGame.f2vwb9aeox.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\QuizCardGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"f2vwb9aeox"},{"Name":"integrity","Value":"sha256-RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/QuizCardGame.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"214716"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/QuizCardGame.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\QuizCardGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"214716"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022RzQr457DpMpVNXOPClugnYvGxxXNl4ZR8gIOzsGD9AU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Simulator.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Simulator.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"214937"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Simulator.mfpa3oss8v.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\Simulator.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mfpa3oss8v"},{"Name":"integrity","Value":"sha256-QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/Simulator.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"214937"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022QqJ8vTGAXPQhX3RXF7QTmj1xAkPuy8BgBvgmcphU/9Q=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/TreasureHuntGame.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\TreasureHuntGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"240809"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/assets/images/TreasureHuntGame.yw2yt9l9j9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\assets\images\TreasureHuntGame.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yw2yt9l9j9"},{"Name":"integrity","Value":"sha256-viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/assets/images/TreasureHuntGame.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"240809"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022viuNMRaZKzWwIhjpNRdjaY4TUQXIlEn5w56x5gOQAR4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/BasePage.1uiseb3eij.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\BasePage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1uiseb3eij"},{"Name":"integrity","Value":"sha256-pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"828129"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\BasePage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"828129"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022pyd9QufeIITG/y9cwblCpH5oIoJxPaeU9b/i7vt1Wcg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js.fni4vypgk0.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\BasePage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fni4vypgk0"},{"Name":"integrity","Value":"sha256-aw7YCNC51kBn0fRh9URWv\u002B4OLjnetHvyIjgj\u002B4Z5P1Y="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3268747"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022aw7YCNC51kBn0fRh9URWv\u002B4OLjnetHvyIjgj\u002B4Z5P1Y=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\BasePage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EYQfb4zNpc3IJjKc/gkt\u002BxODyEC5NEn3Xndt7U0s3RU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3367"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022EYQfb4zNpc3IJjKc/gkt\u002BxODyEC5NEn3Xndt7U0s3RU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js.LICENSE.vg51q489ys.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\BasePage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vg51q489ys"},{"Name":"integrity","Value":"sha256-EYQfb4zNpc3IJjKc/gkt\u002BxODyEC5NEn3Xndt7U0s3RU="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3367"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022EYQfb4zNpc3IJjKc/gkt\u002BxODyEC5NEn3Xndt7U0s3RU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/BasePage.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\BasePage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aw7YCNC51kBn0fRh9URWv\u002B4OLjnetHvyIjgj\u002B4Z5P1Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3268747"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022aw7YCNC51kBn0fRh9URWv\u002B4OLjnetHvyIjgj\u002B4Z5P1Y=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/demo.ggu62m2tlg.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\demo.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ggu62m2tlg"},{"Name":"integrity","Value":"sha256-UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/demo.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"751"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/demo.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\demo.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"751"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022UGZdHQjC2b6kEYuFog22rj8C/kenXVKIcuHEr4PS6bk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.hkmtr1gh73.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\DemoLessonPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hkmtr1gh73"},{"Name":"integrity","Value":"sha256-fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7177760"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\DemoLessonPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7177760"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022fwl2ZZQG27rnAWV46wJBZU27bfOZdpjscklOeBOgGpc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js.2an5qol5yw.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\DemoLessonPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2an5qol5yw"},{"Name":"integrity","Value":"sha256-5rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23677969"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00225rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js.LICENSE.93q2jf4ssj.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\DemoLessonPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"93q2jf4ssj"},{"Name":"integrity","Value":"sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"98850"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\DemoLessonPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"98850"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/DemoLessonPage.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\DemoLessonPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23677969"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00225rDMzKZhpui9qDsPL1jA1gQ0sSNKmjbxtoqYc3JSDkw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/exam-management-router.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\exam-management-router.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"77452"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/exam-management-router.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\exam-management-router.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu\u002BaHE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"256239"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu\u002BaHE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/exam-management-router.js.t7j9vx0o1r.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\exam-management-router.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t7j9vx0o1r"},{"Name":"integrity","Value":"sha256-d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu\u002BaHE="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/exam-management-router.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"256239"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022d5PcTxEs7JSfs9h3sgSBxbFdEP9V5xKcIx/t2xu\u002BaHE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/exam-management-router.kurprhoep7.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\exam-management-router.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kurprhoep7"},{"Name":"integrity","Value":"sha256-xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/exam-management-router.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"77452"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022xMcerNyNChTZVzLsN95xCVqAFCEQO7MPpV72SGX5GLA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\ExamManagementPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-M0kcu2X70QxK9Oz8L3jNYRsjquY\u002BilWOCVRve/PQY5A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"807079"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022M0kcu2X70QxK9Oz8L3jNYRsjquY\u002BilWOCVRve/PQY5A=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js.j7k99epumh.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\ExamManagementPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j7k99epumh"},{"Name":"integrity","Value":"sha256-cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2945412"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js.LICENSE.9mlrlvlkrp.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\ExamManagementPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9mlrlvlkrp"},{"Name":"integrity","Value":"sha256-MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2686"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\ExamManagementPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2686"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\ExamManagementPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2945412"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022cYDfXxUvNrDWRHe8ueT5VCLDMBOb4k6VVBRhj/PNlDo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.z8j7scqg6i.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\ExamManagementPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z8j7scqg6i"},{"Name":"integrity","Value":"sha256-M0kcu2X70QxK9Oz8L3jNYRsjquY\u002BilWOCVRve/PQY5A="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/ExamManagementPage.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"807079"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022M0kcu2X70QxK9Oz8L3jNYRsjquY\u002BilWOCVRve/PQY5A=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/exams.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\exams.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"685"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/exams.j7tki1w2gr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\exams.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j7tki1w2gr"},{"Name":"integrity","Value":"sha256-IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/exams.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"685"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022IbneMz5gVVuN0qk8Ylxgqb0uJPf6EfR5b2wQiqpQsww=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/iconStore.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\iconStore.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"680"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00220Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/iconStore.pyfgcvdtvw.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\iconStore.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pyfgcvdtvw"},{"Name":"integrity","Value":"sha256-0Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/iconStore.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"680"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00220Sx5WHmJrKVCEiJWaoim//wzFBxCT6SWLaKj54pONX0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\IconStorePage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DGzw403\u002BFPkVC3pfRl\u002B7j\u002By0znScVovOOs5nZgrfSVo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"834897"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022DGzw403\u002BFPkVC3pfRl\u002B7j\u002By0znScVovOOs5nZgrfSVo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js.gd6wyxvcha.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\IconStorePage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gd6wyxvcha"},{"Name":"integrity","Value":"sha256-RF4U/ZQBdYxsbCPwLu\u002BojPAm2YG3xLbc8vhKCWF0CNE="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2968513"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022RF4U/ZQBdYxsbCPwLu\u002BojPAm2YG3xLbc8vhKCWF0CNE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js.LICENSE.7yma15gazi.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\IconStorePage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7yma15gazi"},{"Name":"integrity","Value":"sha256-r\u002BdgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2685"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022r\u002BdgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\IconStorePage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-r\u002BdgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2685"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022r\u002BdgLNvldLA45zJ5/xO3omyrEBFLvpSfZSaBrurWB5M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\IconStorePage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RF4U/ZQBdYxsbCPwLu\u002BojPAm2YG3xLbc8vhKCWF0CNE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2968513"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022RF4U/ZQBdYxsbCPwLu\u002BojPAm2YG3xLbc8vhKCWF0CNE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.kqtipf7998.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\IconStorePage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kqtipf7998"},{"Name":"integrity","Value":"sha256-DGzw403\u002BFPkVC3pfRl\u002B7j\u002By0znScVovOOs5nZgrfSVo="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/IconStorePage.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"834897"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022DGzw403\u002BFPkVC3pfRl\u002B7j\u002By0znScVovOOs5nZgrfSVo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/index.hmbk7inl62.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hmbk7inl62"},{"Name":"integrity","Value":"sha256-J\u002BkKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/index.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"675"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022J\u002BkKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/index.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-J\u002BkKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"675"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022J\u002BkKxIjaAwHzJIdCPUWbViw1moo2udJd3/ShKRqAT/M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.7twizm0i3l.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PracticeExamPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7twizm0i3l"},{"Name":"integrity","Value":"sha256-Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"807043"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PracticeExamPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"807043"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Ze6PW0rRc8qiUfH6rox5DcJdFPZt9d7w0di5EnRtwi8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js.LICENSE.9mlrlvlkrp.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PracticeExamPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9mlrlvlkrp"},{"Name":"integrity","Value":"sha256-MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2686"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PracticeExamPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2686"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022MQcgM\u002BraU2VryPcFOi26Ad/wDv29I0xEOiSCFUz7aEg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PracticeExamPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2945343"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js.togz1vcpq9.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PracticeExamPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"togz1vcpq9"},{"Name":"integrity","Value":"sha256-PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/PracticeExamPage.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2945343"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022PbwThmmmeXcoAlAPRc6ulZ7nxLH4FOp/UjUmOxQjaec=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/practiceExams.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\practiceExams.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-v47LinC/xGbLZmho2FuT4\u002BZMC/UwTFmzi0gM5HVSSRE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"683"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022v47LinC/xGbLZmho2FuT4\u002BZMC/UwTFmzi0gM5HVSSRE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/practiceExams.tpmxo2e1f8.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\practiceExams.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tpmxo2e1f8"},{"Name":"integrity","Value":"sha256-v47LinC/xGbLZmho2FuT4\u002BZMC/UwTFmzi0gM5HVSSRE="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/practiceExams.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"683"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022v47LinC/xGbLZmho2FuT4\u002BZMC/UwTFmzi0gM5HVSSRE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/preview.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\preview.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"754"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/preview.vyvfagge59.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\preview.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vyvfagge59"},{"Name":"integrity","Value":"sha256-l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/preview.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"754"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022l1k3rzbQ2wYf7VoXZM5fpg4XjWARBNRu7bh06pTzxZY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.772vr13cpp.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PreviewLessonPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"772vr13cpp"},{"Name":"integrity","Value":"sha256-HRRG\u002BHRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7177674"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HRRG\u002BHRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PreviewLessonPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HRRG\u002BHRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7177674"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HRRG\u002BHRY/A3o1imVSjde/oSRaZ1Uo92Ul1NxG8BOESU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js.LICENSE.93q2jf4ssj.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PreviewLessonPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"93q2jf4ssj"},{"Name":"integrity","Value":"sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"98850"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PreviewLessonPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"98850"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022lb/L5wfVpqM5tWAF9KrAYUL1QPYQ\u002BLMIjK8VN3yPyAc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PreviewLessonPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23677790"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js.qj3zrtvq02.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\PreviewLessonPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qj3zrtvq02"},{"Name":"integrity","Value":"sha256-tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/PreviewLessonPage.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23677790"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022tvseY6rQw2cAAs5jGMzUHkgcZvFxKXEn9FwAhqwqd6c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/question.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\question.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"749"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/question.tvj36u7iep.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\question.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tvj36u7iep"},{"Name":"integrity","Value":"sha256-l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/question.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"749"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022l4q4MmFqjkfX93j1uqwEMVRN6nZpEs9Uge2x7zMsJag=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.1v77uzlqy5.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\QuestionPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1v77uzlqy5"},{"Name":"integrity","Value":"sha256-c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4764115"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\QuestionPage.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4764115"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022c8KgcEgQCp3rFkv4Z3LlXXyKLyJLKboxFxSoAIijhJg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js.ceshysaiu2.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\QuestionPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ceshysaiu2"},{"Name":"integrity","Value":"sha256-BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy\u002Bn2s="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16488638"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy\u002Bn2s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js.LICENSE.7bjzewtjcj.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\QuestionPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7bjzewtjcj"},{"Name":"integrity","Value":"sha256-fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js.LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48146"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js.LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\QuestionPage.js.LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"48146"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022fdX/4zAw9rnqEOoHUOaaqtvDPOMSUA0y1YplU/zBMfs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/QuestionPage.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\QuestionPage.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy\u002Bn2s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16488638"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022BBxrh29h1KZDZAADciNKVALE99K6Uw8B38eZxRy\u002Bn2s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/runtime.e222d9b8rg.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\runtime.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"e222d9b8rg"},{"Name":"integrity","Value":"sha256-9hehO\u002Bu/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/runtime.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4637"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229hehO\u002Bu/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/runtime.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\runtime.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9hehO\u002Bu/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4637"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229hehO\u002Bu/KudEivv4JBtH6OIliURgI0rUuspiyCkt/PQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/runtime.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\runtime.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BErWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22869"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022BErWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/EdTech/reactapp/runtime.js.odtp9r21ta.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\EdTech\reactapp\runtime.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"odtp9r21ta"},{"Name":"integrity","Value":"sha256-BErWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU="},{"Name":"label","Value":"_content/EdTech.Study.Web/EdTech/reactapp/runtime.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22869"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022BErWMPpQm22BKQGkvz1JY3uq/6OOALn6q6uTbZi93MU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:03:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/images/home/<USER>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\home\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"f5ygd9cow5"},{"Name":"integrity","Value":"sha256-48HX\u002B6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM="},{"Name":"label","Value":"_content/EdTech.Study.Web/images/home/<USER>"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3001"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u002248HX\u002B6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 29 May 2025 07:49:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/images/home/<USER>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\home\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-48HX\u002B6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3001"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u002248HX\u002B6g3cYpH4RqkUT8WBzVGHpA6YsMtIyKCg3r0UmM=\u0022"},{"Name":"Last-Modified","Value":"Thu, 29 May 2025 07:49:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/images/home/<USER>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\home\profile-avatar.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"62413"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 29 May 2025 08:02:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/images/home/<USER>">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\home\profile-avatar.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"y2x72g0kd9"},{"Name":"integrity","Value":"sha256-OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI="},{"Name":"label","Value":"_content/EdTech.Study.Web/images/home/<USER>"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"62413"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022OiUBJj2RDOVxo9HnkhrkcE5eNz87JhDmcbJtqy4twWI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 29 May 2025 08:02:24 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/js/common/lesson-init-module.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\common\lesson-init-module.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1102"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:43:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EdTech.Study.Web/js/common/lesson-init-module.kgbamez07j.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\common\lesson-init-module.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kgbamez07j"},{"Name":"integrity","Value":"sha256-hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc="},{"Name":"label","Value":"_content/EdTech.Study.Web/js/common/lesson-init-module.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1102"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022hFaChnEcZNBJf51NAglc9aIfe5g/VgAINU4iugykCAc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 10:43:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>