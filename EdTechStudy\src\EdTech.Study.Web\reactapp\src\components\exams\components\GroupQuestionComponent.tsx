import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  memo,
  Suspense,
} from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Popconfirm,
  Tooltip,
  Collapse,
} from 'antd';
import {
  DeleteOutlined,
  PlusCircleFilled,
  FullscreenOutlined,
  FullscreenExitOutlined,
  CaretRightOutlined,
} from '@ant-design/icons';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';
import { MathContent } from '../../common/MathJax/MathJaxWrapper';
import CustomRichText from '../../common/elements/Text/RichTextEditorComponent/v0/CustomRichText';
import {
  ContentFormatType,
  ExamStatus,
} from '../../../interfaces/exams/examEnums';
import QuestionEditor from '../../quizs/questionEditor/QuestionEditor';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import './groupQuestionComponent.css';
import EmptyQuestionPanel from './EmptyQuestionPanel';
import ModalSelectQuestionType from '../../quizs/practiceEngines/ModalSelectQuestionType';
import QuestionBankModal from '../../quizs/questionBank';
import { QuestionTemplateFactory } from '../../quizs/practiceEngines/questionTemplates';
import practiceLocalization from '../../quizs/localization';
import { QuestionDto } from '../../quizs/questionBank/questionBankTemplate';
import { forEach } from 'lodash';
import { ExamGroupQuestion } from '../../../interfaces/exams/examBase';
import FullscreenContainer from '../../common/Fullscreen/FullscreenContainer';

import { useFullscreenAdapter } from '../../common/Fullscreen';
import useDebouncedCallback from '../../../hooks/apps/useDebouncedCallback';
const { Title, Text } = Typography;
const { Panel } = Collapse;

const DEFAULT_DISPLAY_LIMIT = 10;

export interface GroupQuestionComponentProps {
  currentPosition?: number;
  groupQuestion: ExamGroupQuestion;
  configMode?: boolean;
  onEdit?: (groupQuestion: Partial<ExamGroupQuestion>) => void;
  onDelete?: (groupId: string) => void;
  disabled?: boolean;
  onAddQuestion?: (question: BaseQuestion, position?: number) => void;
  moveQuestion?: (position: number) => void;
}

export const GroupQuestionComponent: React.FC<GroupQuestionComponentProps> & {
  Content: React.FC<any>;
  ConfigQuestion: React.FC<any>;
  PreviewQuestion: React.FC<any>;
} = ({
  currentPosition = 0,
  groupQuestion,
  configMode = false,
  onAddQuestion,
  onEdit,
  onDelete,
  moveQuestion,
  disabled = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);

  const [questionIndex, setQuestionIndex] = useState(currentPosition);
  const [leftPanelWidth, setLeftPanelWidth] = useState(() => {
    // Try to get the cached value from session storage, fallback to 50 if not found
    const cached = sessionStorage.getItem('groupQuestionLeftPanelWidth');
    return cached ? parseInt(cached, 10) : 50;
  });
  const containerRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);
  const [openModalCreateQuestion, setOpenModalCreateQuestion] = useState(false);
  const [modalQuestionBankVisible, setQuestionBankModalVisible] =
    useState(false);

  const handleContentChange = useDebouncedCallback(
    (content: string) => {
      if (onEdit) {
        onEdit({
          content,
        });
      }
    },
    [groupQuestion, onEdit]
  );

  const handleEdit = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleAddQuestion = (type: string, position?: number) => {
    if (type == practiceLocalization.questionbank) {
      setQuestionBankModalVisible(true);
      return;
    }
    const factory = QuestionTemplateFactory.getInstance();
    const newQuestion = factory.create(type);
    if (!newQuestion) {
      return;
    }
    newQuestion.statusEntity = ExamStatus.Draft;
    newQuestion.parentId = groupQuestion.clientId;
    if (onAddQuestion) {
      onAddQuestion(newQuestion, position);
      moveQuestion?.((position ?? 0) + 1);
    }
    // Close modal
    setOpenModalCreateQuestion(false);
  };

  const handleSelectQuestions = (
    selectedQuestions: QuestionDto[],
    position?: number
  ) => {
    setOpenModalCreateQuestion(false);
    setQuestionBankModalVisible(false);
    // Do something with the selected questions
    forEach(selectedQuestions, (questionNew) => {
      const factory = QuestionTemplateFactory.getInstance();
      const newQuestion = factory.createWithProps(
        practiceLocalization.questionbank,
        {
          ...questionNew,
          SyncQuestion: true,
          LastSyncQuestionId: questionNew.Id,
          statusEntity: questionNew.Status,
        }
      );
      if (!newQuestion) {
        return;
      }

      if (onAddQuestion) {
        newQuestion.parentId = groupQuestion.clientId;
        onAddQuestion(newQuestion, position ?? questionIndex + 1);
        moveQuestion?.(position ?? questionIndex + 1);
      }
    });
  };

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (!containerRef.current) return;

      e.preventDefault();
      isDraggingRef.current = true;
      startXRef.current = e.clientX;
      startWidthRef.current =
        containerRef.current.offsetWidth * (leftPanelWidth / 100);

      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      const handleMouseMove = (e: MouseEvent) => {
        if (!isDraggingRef.current || !containerRef.current) return;

        const containerWidth = containerRef.current.offsetWidth;
        const deltaX = e.clientX - startXRef.current;
        const newWidth = startWidthRef.current + deltaX;
        const newWidthPercent = (newWidth / containerWidth) * 100;

        // Constrain between 30% and 70%
        const constrainedWidth = Math.max(30, Math.min(70, newWidthPercent));
        setLeftPanelWidth(constrainedWidth);
        // Save to session storage whenever the width changes
        sessionStorage.setItem(
          'groupQuestionLeftPanelWidth',
          constrainedWidth.toString()
        );
      };

      const handleMouseUp = () => {
        isDraggingRef.current = false;
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    [leftPanelWidth]
  );

  const groupId = groupQuestion.clientId || groupQuestion.id;

  useEffect(() => {
    setQuestionIndex(currentPosition);
  }, [currentPosition]);

  const { isFullscreen, fullscreenRef, handleToggleFullscreen } =
    useFullscreenAdapter();

  if (!groupId) return null;

  return (
    <FullscreenContainer ref={fullscreenRef}>
      <Card
        className={'tailwind-mb-4 ' + (isFullscreen ? 'tailwind-h-screen' : '')}
        title={
          <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
            <Title level={4} className="tailwind-m-0">
              Nhóm câu hỏi
            </Title>
            {configMode && (
              <Space>
                <Tooltip
                  title={
                    isFullscreen
                      ? 'Thoát chế độ toàn màn hình'
                      : 'Toàn màn hình'
                  }
                >
                  <Button
                    type="default"
                    icon={
                      isFullscreen ? (
                        <FullscreenExitOutlined />
                      ) : (
                        <FullscreenOutlined />
                      )
                    }
                    onClick={handleToggleFullscreen}
                  />
                </Tooltip>
                <Popconfirm
                  title="Bạn có chắc chắn muốn xóa nhóm câu hỏi này không?"
                  onConfirm={() => onDelete?.(groupId)}
                >
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    disabled={disabled}
                  />
                </Popconfirm>
              </Space>
            )}
          </div>
        }
      >
        <div
          ref={containerRef}
          className="tailwind-relative tailwind-flex tailwind-w-full tailwind-h-full"
        >
          {/* Left side - Group question content */}
          <div
            className="tailwind-relative tailwind-overflow-auto tailwind-h-full"
            style={{ width: `${leftPanelWidth}%` }}
            onClick={handleEdit}
          >
            <GroupQuestionComponent.Content
              content={groupQuestion.content || ''}
              contentFormat={
                groupQuestion.contentFormat || ContentFormatType.Html
              }
              configMode={configMode && isEditing}
              onContentChange={handleContentChange}
            />
          </div>

          {/* Resizable divider */}
          <div
            className="tailwind-absolute tailwind-h-full tailwind-w-1 tailwind-cursor-col-resize tailwind-bg-gray-200 hover:tailwind-bg-blue-500 tailwind-transition-colors tailwind-z-10"
            style={{
              left: `${leftPanelWidth}%`,
              transform: 'translateX(-50%)',
            }}
            onMouseDown={handleMouseDown}
          />

          {/* Right side - Questions list */}
          <div
            className="tailwind-relative tailwind-overflow-auto tailwind-min-h-[500px]"
            style={{ width: `${100 - leftPanelWidth}%` }}
          >
            <div className="tailwind-px-4">
              {configMode ? (
                <GroupQuestionComponent.ConfigQuestion
                  questions={groupQuestion.questions}
                  onButtonAddQuestionClick={() => {
                    setOpenModalCreateQuestion(true);
                  }}
                />
              ) : (
                <GroupQuestionComponent.PreviewQuestion
                  questions={groupQuestion.questions}
                />
              )}
            </div>
          </div>
        </div>
      </Card>
      <ModalSelectQuestionType
        visible={openModalCreateQuestion}
        onCancel={() => {
          setOpenModalCreateQuestion(false);
        }}
        onSelectType={(type) => handleAddQuestion(type, questionIndex)}
      />
      <QuestionBankModal
        visible={modalQuestionBankVisible}
        onCancel={() => setQuestionBankModalVisible(false)}
        onSelect={(q) => handleSelectQuestions(q, questionIndex)}
      />
    </FullscreenContainer>
  );
};

// Sub-component for displaying group question content
GroupQuestionComponent.Content = ({
  content,
  contentFormat: _contentFormat,
  configMode,
  onContentChange,
}: {
  content: string;
  contentFormat: ContentFormatType;
  configMode?: boolean;
  onContentChange?: (content: string) => void;
}) => {
  if (configMode && onContentChange) {
    return (
      <CustomRichText
        value={content}
        onChangeValue={(_, value) => onContentChange(value ?? '')}
        height={600}
      />
    );
  }

  return (
    <div className="tailwind-my-3">
      <Text className="tailwind-text-gray-700">Nội dung</Text>
      <div className="tailwind-p-4 tailwind-bg-gray-50 tailwind-rounded-lg input-hover-able">
        <MathContent html={content} className="tailwind-text-gray-700" />
      </div>
    </div>
  );
};

GroupQuestionComponent.ConfigQuestion = memo(function ConfigQuestion({
  questions,
  onButtonAddQuestionClick,
}: {
  questions: BaseQuestion[];
  onButtonAddQuestionClick: () => void;
}) {
  const { subjects = [], lessonGrades: grades = [] } = useQuestionContext();
  const [displayLimit, setDisplayLimit] = useState(DEFAULT_DISPLAY_LIMIT);

  const handleLoadMore = () => {
    setDisplayLimit((prevLimit) => prevLimit + DEFAULT_DISPLAY_LIMIT);
  };

  const displayedQuestions = questions.slice(0, displayLimit);
  const hasMoreQuestions = questions.length > displayLimit;

  return (
    <>
      {questions.length > 0 ? (
        <div>
          <Collapse
            defaultActiveKey={
              displayedQuestions.length > 0
                ? [displayedQuestions[0].clientId]
                : []
            }
            className="tailwind-mb-4"
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
          >
            {displayedQuestions.map((question, index) => (
              <Panel
                key={question.clientId}
                header={`Câu hỏi ${index + 1}`}
                className="tailwind-mb-2"
              >
                <Suspense fallback={<div>Loading...</div>}>
                  <QuestionEditor
                    question={question}
                    visible={false}
                    subjects={subjects}
                    grades={grades}
                    onCancel={() => {}}
                    onSave={() => {}}
                  />
                </Suspense>
              </Panel>
            ))}
          </Collapse>

          {hasMoreQuestions && (
            <Button
              type="primary"
              onClick={handleLoadMore}
              className="tailwind-w-full tailwind-mb-4"
            >
              Tải thêm ({questions.length - displayLimit} câu hỏi còn lại)
            </Button>
          )}

          <div className="tailwind-flex">
            <Tooltip title="Thêm câu hỏi">
              <Button
                className="tailwind-grow tailwind-text-[var(--edtt-color-text-primary)]"
                type="dashed"
                color="primary"
                icon={<PlusCircleFilled />}
                onClick={onButtonAddQuestionClick}
              />
            </Tooltip>
          </div>
        </div>
      ) : (
        <EmptyQuestionPanel
          openModalCreateQuestion={onButtonAddQuestionClick}
        />
      )}
    </>
  );
});

GroupQuestionComponent.PreviewQuestion = memo(function PreviewQuestion({
  questions,
}: {
  questions: BaseQuestion[];
}) {
  return <div>PreviewQuestion ({questions.length})</div>;
});

export default GroupQuestionComponent;
