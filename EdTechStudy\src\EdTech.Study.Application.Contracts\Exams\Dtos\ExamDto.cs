﻿using EdTech.Study.Enum;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions;
using EdTech.Study.Questions.Dtos;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Exams.Dtos
{
    public class ExamDto : AuditedEntityDto<Guid>
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string ExamCode { get; set; }
        public ExamType ExamType { get; set; }
        public string ExamPeriod { get; set; }
        public DateTime? ExamDate { get; set; }
        public int? Duration { get; set; }
        public float? TotalScore { get; set; }
        public ExamStatus Status { get; set; }
        public Guid? SubjectId { get; set; }
        public Guid? GradeId { get; set; }
        public ExamSourceType SourceType { get; set; }
        public List<ExamSectionDto> Sections { get; set; }
    }

    public class ExamSectionDto : EntityDto<Guid>
    {
        public string Title { get; set; }
        public string Content { get; set; }

        [EnumDataType(typeof(ContentFormatType))]
        public ContentFormatType? ContentFormat { get; set; }
        public int OrderIndex { get; set; }
        public float? SectionScore { get; set; }
        public string Instructions { get; set; }
        public List<ExamSectionQuestionDto> Questions { get; set; } = new List<ExamSectionQuestionDto>();
        public List<ExamSectionGroupQuestionDto> GroupQuestions { get; set; } = [];
    }

    public class ExamSectionQuestionDto : EntityDto<Guid>
    {
        public Guid ClientId { get; set; }

        public Guid SectionId { get; set; }

        public Guid QuestionId { get; set; }
        /// <summary>
        /// Id Câu hỏi template
        /// Sử dụng để backup khi bật tắt đồng bộ
        /// </summary>
        public Guid? LastSyncQuestionId { get; set; }

        /// <summary>
        /// Sắp xếp
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Điểm số cho câu hỏi này.
        /// </summary>
        public float? Score { get; set; }

        /// <summary>
        /// Đồng bộ hóa với câu hỏi mẫu hay không.
        /// Nếu bằng true thì sẽ lấy câu hỏi đã có trong ngân hàng
        /// Bằng cách liên kết id: QuestionId = exitInDBQuestionId
        /// Nếu bằng False thì sẽ tạo mới câu hỏi, với thông là Question
        /// </summary>
        public bool SyncQuestion { get; set; }

        public string Title { get; set; }
        public string Content { get; set; }
        public ContentFormatType ContentFormat { get; set; }
        public QuestionType QuestionType { get; set; }
        public int Difficulty { get; set; }
        public ExamStatus Status { get; set; }
        public string? Comment { get; set; }
        public Guid? SubjectId { get; set; }
        public Guid? GradeId { get; set; }
        public bool ShuffleOptions { get; set; }
        public string? Explanation { get; set; }
        public ExamSourceType SourceType { get; set; }
        public string? Topics { get; set; }
        public string? Tags { get; set; }
        public IList<QuestionOptionDto> Options { get; set; }
        public IList<FillInBlankAnswerDto> FillInBlankAnswers { get; set; }
        public IList<MatchingAnswerDto> MatchingAnswers { get; set; }
        public IList<MatchingItemDto> MatchingItems { get; set; }
        public string? CorrectAnswer { get; set; }
        public List<ItemOrder>? SortOrders { get; set; }
    }

    public class ExamSectionGroupQuestionDto : EntityDto<Guid>
    {
        public Guid? ClientId { get; set; }

        /// <summary>
        /// ID Phan thi
        /// </summary>
        public Guid SectionId { get; set; }

        /// <summary>
        /// Id Nhom cau hoi
        /// </summary>
        public Guid GroupQuestionId { get; set; }

        /// <summary>
        /// Nội dung câu hỏi
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung câu hỏi: html, text
        /// </summary>
        public ContentFormatType ContentFormat { get; set; } = ContentFormatType.Html;

        /// <summary>
        /// Hướng dẫn làm bài
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// Đảm bảo duy nhất mỗi request thêm
        /// </summary>
        public string? IdempotentKey { get; set; }

        /// <summary>
        /// Sắp xếp
        /// </summary>
        public int Order { get; set; } = 0;

        /// <summary>
        /// Câu hỏi trong nhóm
        /// </summary>
        public List<ExamSectionQuestionDto> Questions { get; set; } = new List<ExamSectionQuestionDto>();
    }
}