﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EdTech.Study.Enum;
using EdTech.Study.Exams;
using EdTech.Study.Grade;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Uow;

namespace EdTech.Study.Data
{
    public class ExamSeedDataContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<Subject.Subject, Guid> _subjectRepository;
        private readonly IRepository<LessonGrade, Guid> _gradeRepository;
        private readonly IRepository<ExamEntity, Guid> _examRepository;
        private readonly IRepository<SectionEntity, Guid> _examSectionRepository;
        private readonly IRepository<QuestionEntity, Guid> _questionRepository;
        private readonly IRepository<QuestionOptionEntity, Guid> _questionOptionRepository;
        private readonly IRepository<FillInBlankAnswerEntity, Guid> _fillInBlankAnswerRepository;
        private readonly IRepository<MatchingItemEntity, Guid> _matchingItemRepository;
        private readonly IRepository<MatchingAnswerEntity, Guid> _matchingAnswerRepository;
        private readonly IRepository<GroupQuestionEntity, Guid> _groupQuestionRepository;
        private readonly IRepository<SectionGroupQuestionEntity, Guid> _sectionGroupQuestionRepository;
        private readonly IGuidGenerator _guidGenerator;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        private readonly Random _random = new Random();

        public ExamSeedDataContributor(
            IRepository<Subject.Subject, Guid> subjectRepository,
            IRepository<LessonGrade, Guid> gradeRepository,
            IRepository<ExamEntity, Guid> examRepository,
            IRepository<SectionEntity, Guid> examSectionRepository,
            IRepository<QuestionEntity, Guid> questionRepository,
            IRepository<QuestionOptionEntity, Guid> questionOptionRepository,
            IRepository<FillInBlankAnswerEntity, Guid> fillInBlankAnswerRepository,
            IRepository<MatchingItemEntity, Guid> matchingItemRepository,
            IRepository<MatchingAnswerEntity, Guid> matchingAnswerRepository,
            IGuidGenerator guidGenerator,
            IUnitOfWorkManager unitOfWorkManager,
            IRepository<GroupQuestionEntity, Guid> groupQuestionRepository,
            IRepository<SectionGroupQuestionEntity, Guid> sectionGroupQuestionRepository)
        {
            _subjectRepository = subjectRepository;
            _gradeRepository = gradeRepository;
            _examRepository = examRepository;
            _examSectionRepository = examSectionRepository;
            _questionRepository = questionRepository;
            _questionOptionRepository = questionOptionRepository;
            _fillInBlankAnswerRepository = fillInBlankAnswerRepository;
            _matchingItemRepository = matchingItemRepository;
            _matchingAnswerRepository = matchingAnswerRepository;
            _guidGenerator = guidGenerator;
            _unitOfWorkManager = unitOfWorkManager;
            _groupQuestionRepository = groupQuestionRepository;
            _sectionGroupQuestionRepository = sectionGroupQuestionRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            // Check if data already exists
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true))
            {
                if (await _examRepository.GetCountAsync() > 0)
                {
                    await uow.CompleteAsync();
                    return; // Already seeded
                }
                var subjects = await _subjectRepository.GetListAsync();
                var grades = await _gradeRepository.GetListAsync();

                // Seed exams
                await SeedExamsAsync(subjects, grades);

                await uow.CompleteAsync();
            }
        }

        private async Task SeedExamsAsync(List<Subject.Subject> subjects, List<LessonGrade> grades)
        {
            // Create 10 sample exams
            for (int i = 0; i < 20; i++)
            {
                await CreateRandomExamAsync(subjects, grades);
            }
        }

        private async Task CreateRandomExamAsync(List<Subject.Subject> subjects, List<LessonGrade> grades)
        {
            // Create random exam
            var subject = subjects[_random.Next(subjects.Count)];
            var grade = grades[_random.Next(grades.Count)];

            // Use specific values instead of Enum.GetValues
            var examTypes = new[] { ExamType.Test15Minutes, ExamType.Test45Minutes, ExamType.QualificationExam,
                                   ExamType.MidTermExam, ExamType.FinalExam, ExamType.MockExam,
                                   ExamType.NationalHighSchoolExam, ExamType.Default };
            var sourceTypes = new[] { ExamSourceType.Default, ExamSourceType.Banked };

            var examType = examTypes[_random.Next(examTypes.Length)];
            var sourceType = sourceTypes[_random.Next(sourceTypes.Length)];

            var examDate = DateTime.Now.AddDays(_random.Next(-30, 30));

            var exam = new ExamEntity(_guidGenerator.Create())
            {
                Title = $"Đề thi {GetExamTypeText(examType)} môn {subject.Name} - {grade.Name}",
                Description = $"Đề thi {GetExamTypeText(examType)} môn {subject.Name} dành cho {grade.Name}",
                ExamCode = $"{subject.Code}{grade.Name.Replace("Lớp ", "")}-{_random.Next(1000, 9999)}",
                ExamType = examType,
                ExamPeriod = GetRandomExamPeriod(),
                ExamDate = examDate,
                Duration = _random.Next(15, 180),
                TotalScore = 10.0f,
                Status = ExamStatus.Published,
                SubjectId = subject.Id,
                GradeId = grade.Id,
                SourceType = sourceType,
                IdempotentKey = Guid.NewGuid().ToString(),
                Source = "Seed Data",
                Sections = new List<SectionEntity>()
            };

            // Save exam
            await _examRepository.InsertAsync(exam);

            // Create sections
            var sectionCount = _random.Next(1, 5);
            float totalSectionScore = 0;

            for (int i = 0; i < sectionCount; i++)
            {
                float sectionScore = (float)Math.Round(10.0f / sectionCount, 1);
                totalSectionScore += sectionScore;

                // Adjust last section score to ensure total is exactly 10
                if (i == sectionCount - 1 && totalSectionScore != 10.0f)
                {
                    sectionScore = 10.0f - (totalSectionScore - sectionScore);
                }

                var section = await CreateExamSectionAsync(exam, i, sectionScore);

                // Create questions for this section
                await CreateQuestionsForSectionAsync(section, subject, grade);

                // Create group questions for this section
                await CreateGroupQuestionQuestionsForSectionAsync(section, subject, grade);
            }
        }

        private async Task<SectionEntity> CreateExamSectionAsync(ExamEntity exam, int orderIndex, float sectionScore)
        {
            var sectionTypes = new[] { "Trắc nghiệm", "Tự luận", "Thực hành", "Điền từ", "Nối cặp" };
            var sectionType = sectionTypes[_random.Next(sectionTypes.Length)];

            var section = new SectionEntity(_guidGenerator.Create())
            {
                ExamId = exam.Id,
                Title = $"Phần {orderIndex + 1}: {sectionType}",
                Content = $"Nội dung phần thi {sectionType}",
                ContentFormat = ContentFormatType.Html,
                OrderIndex = orderIndex,
                SectionScore = sectionScore,
                Instructions = $"Hướng dẫn làm bài phần {sectionType}",
                SourceType = exam.SourceType,
                IdempotentKey = Guid.NewGuid().ToString(),
                Source = "Seed Data",
                Questions = new List<SectionQuestionEntity>()
            };

            await _examSectionRepository.InsertAsync(section);
            return section;
        }

        private async Task CreateQuestionsForSectionAsync(SectionEntity section, Subject.Subject subject, LessonGrade grade)
        {
            var questionCount = _random.Next(10, 50);
            var questionTypes = new[] { QuestionType.MultipleChoice, QuestionType.Fillblanks, QuestionType.Matching };

            float questionScore = (section.SectionScore.HasValue ? section.SectionScore.Value : 1) / questionCount;
            questionScore = (float)Math.Round(questionScore, 2);

            for (int i = 0; i < questionCount; i++)
            {
                var questionType = questionTypes[_random.Next(questionTypes.Length - 1)];
                var question = await CreateQuestionAsync(questionType, subject, grade, section.SourceType);

                // Create link between section and question
                var sectionQuestion = new SectionQuestionEntity(Guid.NewGuid())
                {
                    SectionId = section.Id,
                    QuestionId = question.Id,
                    Order = i,
                    SyncQuestion = false,
                    Score = questionScore
                };

                section.Questions.Add(sectionQuestion);
                await _examSectionRepository.UpdateAsync(section);
            }
        }

        private async Task<QuestionEntity> CreateQuestionAsync(QuestionType questionType, Subject.Subject subject, LessonGrade grade, ExamSourceType sourceType)
        {
            var question = new QuestionEntity(_guidGenerator.Create())
            {
                Content = GetRandomQuestionContent(questionType, subject.Name),
                ContentFormat = ContentFormatType.Html,
                QuestionType = questionType,
                Difficulty = _random.Next(1, 6),
                Status = ExamStatus.Published,
                SubjectId = subject.Id,
                GradeId = grade.Id,
                Explanation = "Đây là giải thích chi tiết cho câu hỏi này.",
                SourceType = sourceType,
                Topics = $"{subject.Name},{grade.Name}",
                Tags = "Seed,Example,Test",
                IdempotentKey = Guid.NewGuid().ToString(),
                Source = "Seed Data"
            };

            await _questionRepository.InsertAsync(question);

            // Create different answer types based on question type
            switch (questionType)
            {
                case QuestionType.MultipleChoice:
                    await CreateMultipleChoiceOptionsAsync(question);
                    break;
                case QuestionType.Fillblanks:
                    await CreateFillInBlankAnswersAsync(question);
                    break;
                case QuestionType.Matching:
                    await CreateMatchingItemsAndAnswersAsync(question);
                    break;
            }

            return question;
        }

        private async Task CreateMultipleChoiceOptionsAsync(QuestionEntity question)
        {
            var optionCount = _random.Next(2, 6);
            var correctOptionIndex = _random.Next(optionCount);

            for (int i = 0; i < optionCount; i++)
            {
                var option = new QuestionOptionEntity(_guidGenerator.Create())
                {
                    QuestionId = question.Id,
                    Content = $"Đáp án {GetOptionLabel(i)}",
                    ContentFormat = ContentFormatType.Html,
                    IsCorrect = (i == correctOptionIndex),
                    Order = i,
                    Explanation = i == correctOptionIndex ? "Đây là đáp án đúng" : "Đây không phải đáp án đúng",
                    Score = i == correctOptionIndex ? 1.0f : 0.0f
                };

                await _questionOptionRepository.InsertAsync(option);
            }
        }

        private async Task CreateFillInBlankAnswersAsync(QuestionEntity question)
        {
            var blankCount = _random.Next(1, 4);

            for (int i = 0; i < blankCount; i++)
            {
                var answer = new FillInBlankAnswerEntity(_guidGenerator.Create())
                {
                    QuestionId = question.Id,
                    BlankIndex = i,
                    CorrectAnswers = new List<string> { $"Đáp án đúng {i + 1}", $"Đáp án phụ {i + 1}" },
                    CaseSensitive = _random.Next(2) == 0,
                    Feedback = "Phản hồi khi trả lời đúng.",
                    Score = 1.0f / blankCount
                };

                await _fillInBlankAnswerRepository.InsertAsync(answer);
            }
        }

        private async Task CreateMatchingItemsAndAnswersAsync(QuestionEntity question)
        {
            var pairCount = _random.Next(2, 5);
            var premiseItems = new List<MatchingItemEntity>();
            var responseItems = new List<MatchingItemEntity>();

            // Create premise items
            for (int i = 0; i < pairCount; i++)
            {
                var premise = new MatchingItemEntity(_guidGenerator.Create())
                {
                    QuestionId = question.Id,
                    Type = MatchingItemType.Premise,
                    Content = $"Tiền đề {i + 1}",
                    ContentFormat = ContentFormatType.Html,
                    Order = i
                };

                await _matchingItemRepository.InsertAsync(premise);
                premiseItems.Add(premise);
            }

            // Create response items
            for (int i = 0; i < pairCount; i++)
            {
                var response = new MatchingItemEntity(_guidGenerator.Create())
                {
                    QuestionId = question.Id,
                    Type = MatchingItemType.Response,
                    Content = $"Phản hồi {i + 1}",
                    ContentFormat = ContentFormatType.Html,
                    Order = i
                };

                await _matchingItemRepository.InsertAsync(response);
                responseItems.Add(response);
            }

            // Create matching answers
            // Shuffle the responses to create randomized matching
            var shuffledResponses = responseItems.OrderBy(x => Guid.NewGuid()).ToList();

            for (int i = 0; i < pairCount; i++)
            {
                var answer = new MatchingAnswerEntity(_guidGenerator.Create())
                {
                    QuestionId = question.Id,
                    PremiseId = premiseItems[i].Id,
                    ResponseId = shuffledResponses[i].Id,
                    Score = 1.0f / pairCount,
                    Feedback = "Phản hồi khi nối đúng cặp."
                };

                await _matchingAnswerRepository.InsertAsync(answer);
            }
        }

        private async Task<GroupQuestionEntity> CreateGroupQuestionAsync(CreateUpdateGroupQuestionDto createUpdateGroupQuestionDto)
        {
            var groupQuestion = new GroupQuestionEntity(_guidGenerator.Create())
            {
                Content = createUpdateGroupQuestionDto.Content,
                ContentFormat = ContentFormatType.Html,
                Instructions = createUpdateGroupQuestionDto.Instructions,
                IdempotentKey = Guid.NewGuid().ToString(),
            };

            await _groupQuestionRepository.InsertAsync(groupQuestion);
            return groupQuestion;
        }

        private async Task CreateGroupQuestionQuestionsForSectionAsync(SectionEntity section, Subject.Subject subject, LessonGrade grade)
        {
            // Determine how many group questions to create for this section (1-3)
            var groupQuestionCount = _random.Next(1, 4);

            for (int i = 0; i < groupQuestionCount; i++)
            {
                // Create a group question
                var groupQuestionDto = new CreateUpdateGroupQuestionDto
                {
                    Content = $"Đọc đoạn văn sau và trả lời các câu hỏi: Đây là đoạn văn mẫu về {subject.Name} dành cho {grade.Name}. " +
                             $"Đoạn văn này có thể chứa các thông tin, dữ liệu, hoặc tình huống liên quan đến môn học.",
                    ContentFormat = ContentFormatType.Html,
                    Instructions = $"Dựa vào đoạn văn trên, hãy trả lời các câu hỏi sau đây về {subject.Name}.",
                    IdempotentKey = Guid.NewGuid().ToString()
                };

                var groupQuestion = await CreateGroupQuestionAsync(groupQuestionDto);

                // Create a SectionGroupQuestionEntity to link the group question to the section
                var sectionGroupQuestion = new SectionGroupQuestionEntity(_guidGenerator.Create())
                {
                    SectionId = section.Id,
                    GroupQuestionId = groupQuestion.Id,
                    Order = i,
                    SyncGroupQuestion = _random.Next(2) == 0 // Randomly set to true or false
                };

                // Insert the SectionGroupQuestion
                await _sectionGroupQuestionRepository.InsertAsync(sectionGroupQuestion);

                // Create some questions for this group question
                var questionCount = _random.Next(2, 6);
                var questionTypes = new[] { QuestionType.MultipleChoice, QuestionType.Fillblanks };

                for (int j = 0; j < questionCount; j++)
                {
                    var questionType = questionTypes[_random.Next(questionTypes.Length)];
                    var question = await CreateQuestionAsync(questionType, subject, grade, section.SourceType);

                    // Set the GroupQuestionId to associate the question with the group question
                    question.GroupQuestionId = groupQuestion.Id;
                    question.Title = $"Câu hỏi {j + 1} cho nhóm câu hỏi";
                    await _questionRepository.UpdateAsync(question);
                }
            }
        }

        #region Helper Methods

        private string GetExamTypeText(ExamType examType)
        {
            return examType switch
            {
                ExamType.Test15Minutes => "Kiểm tra 15 phút",
                ExamType.Test45Minutes => "Kiểm tra 45 phút",
                ExamType.QualificationExam => "Thi điều kiện",
                ExamType.MidTermExam => "Giữa kỳ",
                ExamType.FinalExam => "Cuối kỳ",
                ExamType.MockExam => "Thi thử",
                ExamType.NationalHighSchoolExam => "THPT Quốc gia",
                ExamType.Default => "Mặc định",
                _ => "Đề thi"
            };
        }

        private string GetRandomExamPeriod()
        {
            var periods = new[]
            {
                "Học kỳ 1", "Học kỳ 2",
                "Học kỳ I", "Học kỳ II",
                "15 phút", "1 tiết", "Cuối kỳ"
            };

            return periods[_random.Next(periods.Length)];
        }

        private string GetRandomQuestionContent(QuestionType questionType, string subjectName)
        {
            var contents = new Dictionary<QuestionType, string[]>
            {
                {
                    QuestionType.MultipleChoice, new[]
                    {
                        $"Câu hỏi trắc nghiệm về {subjectName} số 1?",
                        $"Đâu là đáp án đúng cho {subjectName}?",
                        $"Trong môn {subjectName}, điều nào sau đây là đúng?",
                        $"Chọn câu trả lời đúng nhất về {subjectName}:",
                        $"Theo {subjectName}, đâu là phương án chính xác?"
                    }
                },
                {
                    QuestionType.Fillblanks, new[]
                    {
                        $"Điền vào chỗ trống: {subjectName} là ____ quan trọng trong chương trình học.",
                        $"Hoàn thành câu sau: Trong môn {subjectName}, ____ là yếu tố cơ bản nhất.",
                        $"Theo lý thuyết {subjectName}, ____ được định nghĩa là ____.",
                        $"{subjectName} bao gồm các phần: ____, ____ và ____.",
                        $"____ là khái niệm cốt lõi trong môn {subjectName}."
                    }
                },
                {
                    QuestionType.Matching, new[]
                    {
                        $"Nối các khái niệm {subjectName} với định nghĩa tương ứng:",
                        $"Ghép các thuật ngữ {subjectName} với ví dụ phù hợp:",
                        $"Nối các nhân vật quan trọng trong {subjectName} với thành tựu của họ:",
                        $"Ghép các sự kiện trong {subjectName} với thời gian xảy ra:",
                        $"Nối các công thức {subjectName} với ứng dụng tương ứng:"
                    }
                }
            };

            var contentArray = contents[questionType];
            return contentArray[_random.Next(contentArray.Length)];
        }

        private string GetOptionLabel(int index)
        {
            return index switch
            {
                0 => "A",
                1 => "B",
                2 => "C",
                3 => "D",
                4 => "E",
                5 => "F",
                _ => $"{index + 1}"
            };
        }

        #endregion
    }
}