import { Button } from 'antd';
import { IBookMarkItem } from '../../../interfaces/commonInterfaces';
import { buildTreeStructure } from './examEditorSideMenuBuilder';
import { memo, useEffect, useMemo, useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  MenuOutlined,
  BookFilled,
  DownOutlined,
  RightOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import TreeMenuItem from '../../common/Trees/TreeMenuItem';

// Enhanced type that includes children array
export type TreeItemType = IBookMarkItem & { children: TreeItemType[] };

// Sortable tree menu item component for level 1 items
const SortableTreeMenuItem = ({
  item,
  index,
  parentId,
  moveItem,
  onClick,
  level = 0,
  autoExpand = false,
}: {
  item: TreeItemType;
  index: number;
  parentId: string | null;
  autoExpand?: boolean;
  moveItem: (
    dragIndex: number,
    hoverIndex: number,
    parentId: string | null
  ) => void;
  onClick?: (id: string) => any;
  level?: number;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = item.children && item.children.length > 0;

  // Only enable drag for level 1 items
  const isDraggable = [1, 2].includes(level);

  const [{ isDragging }, drag] = useDrag({
    type: `TREE_ITEM_${parentId || 'ROOT'}`,
    item: () => ({ id: item.id, index, parentId }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: () => isDraggable,
  });

  const [, drop] = useDrop({
    accept: `TREE_ITEM_${parentId || 'ROOT'}`,
    hover: (
      draggedItem: { id: string; index: number; parentId: string | null },
      _monitor
    ) => {
      if (draggedItem.index === index || draggedItem.parentId !== parentId) {
        return;
      }
      moveItem(draggedItem.index, index, parentId);
      draggedItem.index = index;
    },
  });

  // Handler for toggling expansion
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Base menu item that will be displayed at all levels
  const menuItem = (
    <div
      // href={'#' + item.id}
      className={
        'tree-menu-link tailwind-btn tailwind-block tailwind-text-nowrap tailwind-text-ellipsis tailwind-overflow-hidden tailwind-cursor-pointer' +
        (level <= 1 ? 'tailwind-font-medium ' : '')
      }
      onClick={(e) => {
        if (hasChildren && level >= 1) {
          // If this is a parent item with children and level >= 1, toggle expansion
          toggleExpand(e);
        } else {
          // Otherwise, proceed with the click handler
          e.preventDefault();
          onClick && onClick(item.id);
        }
      }}
    >
      <div className="tailwind-flex tailwind-gap-2 tailwind-items-center">
        {isDraggable && (
          <span className="tailwind-cursor-move tailwind-text-gray-400 hover:tailwind-text-gray-600">
            <MenuOutlined />
          </span>
        )}
        {item.marked && (
          <span className="tailwind-text-primary">
            <BookFilled />
          </span>
        )}
        {!item.marked && (
          <span className="tailwind-text-primary">
            {item.icon ||
              (hasChildren ? (
                isExpanded ? (
                  <DownOutlined />
                ) : (
                  <RightOutlined />
                )
              ) : (
                <RightOutlined />
              ))}
          </span>
        )}
        <span>{item.displayName}</span>
      </div>
    </div>
  );

  const itemContent = (
    <div
      ref={isDraggable ? (node) => drag(drop(node)) : undefined}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className={isDraggable ? 'tailwind-cursor-move' : ''}
    >
      {menuItem}
    </div>
  );

  useEffect(() => {
    if (autoExpand) {
      setIsExpanded(true);
    }
  }, [autoExpand]);

  // If this is a root level item or level 0, render normally
  if (level < 1 || !hasChildren) {
    return (
      <div className="tailwind-py-1">
        {itemContent}
        {hasChildren && (
          <div className="tailwind-pl-4">
            {item.children.map((child, childIndex) => (
              <SortableTreeMenuItem
                key={child.id}
                item={child}
                index={childIndex}
                parentId={item.id}
                moveItem={moveItem}
                level={level + 1}
                onClick={onClick}
                autoExpand={autoExpand}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // For level >= 1 with children, use collapsible style
  return (
    <div className="tailwind-py-1">
      {itemContent}
      {isExpanded && (
        <div className="tailwind-pl-4">
          {item.children.map((child, childIndex) => (
            <SortableTreeMenuItem
              key={child.id}
              item={child}
              index={childIndex}
              parentId={item.id}
              moveItem={moveItem}
              level={level + 1}
              onClick={onClick}
              autoExpand={autoExpand}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const ExamEditorSideMenu = ({
  showMenu,
  items,
  onClose,
  onSaveMenuConfig,
  onClick,
}: {
  showMenu: boolean;
  onClose: (treeItems: TreeItemType[]) => void;
  items: IBookMarkItem[];
  onClick?: (id: string) => any;
  onSaveMenuConfig?: (treeItems: TreeItemType[]) => void;
}) => {
  const initialTreeItems = useMemo(() => buildTreeStructure(items), [items]);
  const [treeItems, setTreeItems] = useState<TreeItemType[]>(initialTreeItems);
  const [isSorting, setIsSorting] = useState(false);
  // Update tree items when original items change
  useMemo(() => {
    setTreeItems(initialTreeItems);
  }, [initialTreeItems]);

  // Function to move an item in the array
  const moveItem = (
    dragIndex: number,
    hoverIndex: number,
    parentId: string | null
  ) => {
    setTreeItems((prevItems) => {
      const newItems = [...prevItems];

      if (parentId === null) {
        // Moving root level items
        const draggedItem = newItems[dragIndex];
        newItems.splice(dragIndex, 1);
        newItems.splice(hoverIndex, 0, draggedItem);

        // Update order property
        newItems.forEach((item, index) => {
          item.order = index;
        });
      } else {
        // Find the parent item that contains the children being reordered
        const findAndUpdateChildren = (items: TreeItemType[]): boolean => {
          for (let i = 0; i < items.length; i++) {
            if (items[i].id === parentId) {
              // Found the parent, update its children
              const children = [...items[i].children];
              const draggedChild = children[dragIndex];
              children.splice(dragIndex, 1);
              children.splice(hoverIndex, 0, draggedChild);

              // Update order property for children
              children.forEach((child, index) => {
                child.order = index;
              });

              items[i].children = children;
              return true;
            }

            // Recursively search in children
            if (items[i].children.length > 0) {
              if (findAndUpdateChildren(items[i].children)) {
                return true;
              }
            }
          }
          return false;
        };

        findAndUpdateChildren(newItems);
      }

      return newItems;
    });
  };

  return (
    <div
      className="tailwind-relative tailwind-h-full tailwind-w-full"
      title={'Menu'}
    >
      <Button
        danger
        icon={<CloseOutlined />}
        variant="text"
        onClick={() => {
          onClose(treeItems);
        }}
        className="tailwind-absolute tailwind-top-0 tailwind-right-0 tailwind-m-2"
      ></Button>
      <div className="tailwind-flex tailwind-justify-start tailwind-my-2">
        {!isSorting ? (
          <Button
            type={isSorting ? 'primary' : 'default'}
            className={isSorting ? 'tailwind-bg-green-500' : ''}
            onClick={() => {
              setIsSorting(!isSorting);
            }}
            icon={<MenuOutlined />}
          >
            Sắp xếp
          </Button>
        ) : (
          <Button
            type="primary"
            onClick={() => {
              onSaveMenuConfig?.(treeItems);
              setIsSorting(false);
            }}
            icon={<MenuOutlined />}
          >
            Lưu thay đổi
          </Button>
        )}
      </div>

      {isSorting ? (
        <DndProvider backend={HTML5Backend}>
          <div className="tailwind-py-5 tailwind-overflow-y-auto menu-item-panel">
            {treeItems.map((item, index) => (
              <SortableTreeMenuItem
                key={item.id}
                item={item}
                index={index}
                parentId={null}
                moveItem={moveItem}
                onClick={onClick}
                autoExpand={isSorting}
              />
            ))}
          </div>
        </DndProvider>
      ) : (
        <div className="tailwind-py-5 tailwind-overflow-y-auto menu-item-panel">
          {treeItems.map((item) => (
            <TreeMenuItem
              key={item.id}
              item={item}
              onClick={onClick}
              autoExpand={isSorting}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default memo(ExamEditorSideMenu);
