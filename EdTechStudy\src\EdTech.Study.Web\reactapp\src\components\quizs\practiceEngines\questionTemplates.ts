import { Guid } from 'guid-typescript';
import {
  QuizQuestion,
  BaseQuestion,
  QuestionType,
} from '../../../interfaces/quizs/questionBase';
import { FillBlanksQuestion } from '../../../interfaces/quizs/fillblanks.interfaces';
import { createMultiSelectQuestionTemplate } from '../multiselect';
import { createEssayQuestionTemplate } from '../essay';
import {
  ContentFormatType,
  MatchingItemType,
} from '../../../interfaces/exams/examEnums';
import { MatchingQuestion } from '../../../interfaces/quizs/mapping.interface';

export const createQuizQuestionTemplate = (
  title: string = 'Câu hỏi trắc nghiệm'
): QuizQuestion => {
  const id = Guid.create().toString();
  return {
    clientId: id,
    type: 'quiz',
    title,
    content: 'Nhập nội dung câu hỏi ở đây',
    difficulty: 1,
    syncQuestion: false,
    questionType: QuestionType.SingleChoice,
    options: [
      {
        id: undefined,
        isCorrect: true,
        clientId: Guid.create().toString(),
        content: 'Phương án A',
        contentFormat: ContentFormatType.Html,
        order: 0,
      },
      {
        id: undefined,
        content: 'Phương án B',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 1,
      },
      {
        id: undefined,
        content: 'Phương án C',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 2,
      },
      {
        id: undefined,
        content: 'Phương án D',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 3,
      },
    ],
    explanation: 'Thêm giải thích đáp án',
    points: 1,
  };
};

export const createMatchingQuestionTemplate = (
  title: string = 'Câu hỏi nối đáp án'
): MatchingQuestion => {
  const questionId = Guid.create().toString();
  
  // Tạo GUIDs riêng cho từng item để đảm bảo tính duy nhất
  const premise1Id = Guid.create().toString();
  const premise2Id = Guid.create().toString();
  const premise3Id = Guid.create().toString();
  const response1Id = Guid.create().toString();
  const response2Id = Guid.create().toString();
  const response3Id = Guid.create().toString();
  
  return {
    clientId: questionId,
    type: 'matching',
    title,
    difficulty: 1,
    syncQuestion: false,
    questionType: QuestionType.Matching,
    content: 'Nối các mục từ cột A với các mục tương ứng trong cột B',
    matchingItems: [
      {
        clientId: premise1Id,
        content: 'Mục 1',
        type: MatchingItemType.Premise,
        order: 0,
      },
      {
        clientId: premise2Id,
        content: 'Mục 2',
        type: MatchingItemType.Premise,
        order: 1,
      },
      {
        clientId: premise3Id,
        content: 'Mục 3',
        type: MatchingItemType.Premise,
        order: 2,
      },
      {
        clientId: response1Id,
        content: 'Mục A',
        type: MatchingItemType.Response,
        order: 0,
      },
      {
        clientId: response2Id,
        content: 'Mục B',
        type: MatchingItemType.Response,
        order: 1,
      },
      {
        clientId: response3Id,
        content: 'Mục C',
        type: MatchingItemType.Response,
        order: 2,
      },
    ],
    shuffleItems: true,
    matchingAnswers: [
      {
        id: undefined,
        clientId: Guid.create().toString(),
        premiseId: premise1Id,
        responseId: response1Id,
        isCorrect: true,
        content: '',
        contentFormat: ContentFormatType.Html,
        order: 0,
      },
      {
        id: undefined,
        clientId: Guid.create().toString(),
        premiseId: premise2Id,
        responseId: response2Id,
        isCorrect: true,
        content: '',
        contentFormat: ContentFormatType.Html,
        order: 1,
      },
      {
        id: undefined,
        clientId: Guid.create().toString(),
        premiseId: premise3Id,
        responseId: response3Id,
        isCorrect: true,
        content: '',
        contentFormat: ContentFormatType.Html,
        order: 2,
      },
    ],
  };
};

export const createFillBlanksQuestionTemplate = (
  title: string = 'Câu hỏi điền từ'
): FillBlanksQuestion => {
  const id = Guid.create().toString();
  return {
    clientId: id,
    type: 'fillblanks',
    questionType: QuestionType.FillInBlank,
    difficulty: 1,
    syncQuestion: false,
    title,
    content: 'Hà Nội là ____ của Việt Nam.',
    blanks: [
      {
        clientId: Guid.create().toString(),
        correctAnswer: 'thủ đô',
        alternativeAnswers: ['Thủ đô'],
        hint: 'Trung tâm chính trị của đất nước',
      },
    ],
    explanation: 'Hà Nội là thủ đô của Việt Nam.',
    caseSensitive: false,
    showHints: true,
    points: 1,
  };
};

export { createEssayQuestionTemplate, createMultiSelectQuestionTemplate };

export class QuestionTemplateFactory {
  private static instance: QuestionTemplateFactory;
  private templateMaps: Record<string, (props?: any) => BaseQuestion>;

  private constructor() {
    this.templateMaps = {};
  }

  static getInstance(): QuestionTemplateFactory {
    if (!QuestionTemplateFactory.instance) {
      QuestionTemplateFactory.instance = new QuestionTemplateFactory();
    }
    return QuestionTemplateFactory.instance;
  }

  register(type: string, callback: (props?: any) => BaseQuestion) {
    this.templateMaps[type] = callback;
  }

  create(type: string): BaseQuestion | null {
    const creation = this.templateMaps[type];
    return creation ? creation() : null;
  }

  createWithProps(type: string, props: any): BaseQuestion | null {
    const creation = this.templateMaps[type];
    return creation ? creation(props) : null;
  }
}
