/**
 * [ExamEditorEnhancedContainer]
 * Connect to redux store for "ExamEditorEnhanced" component
 */

import { connect } from 'react-redux';
import {
  fetchExamById,
  updateExamData,
  createExam,
  setCurrentExam,
  updateCurrentExamField,
  addSection,
  updateSection,
  removeSection,
  addQuestion,
  updateQuestion,
  removeQuestion,
  reorderSections,
  reorderQuestions,
  updateExamStatus,
  setStateSavePending,
  updateGroupQuestion,
  removeGroupQuestion,
  addGroupQuestion,
  setOrderedIndexItems, // Add this import
} from '../../../store/slices/ExamSlices/examDataManagerSlice';
import './examEditor.css';
import { ExamEditorDispatchToProps } from './examEditorEnhanced.interface';
import ExamEditorEnhanced from './ExamEditorEnhanced';

// Map Redux state to component props
const mapStateToProps = (state: any) => ({
  currentExam: state.examDataManager.currentExam,
  loading: state.examDataManager.loading,
  error: state.examDataManager.error,
  isDirty: state.examDataManager.isDirty,
  stateSavePending: state.examDataManager.stateSavePending,
  indexItems: state.examDataManager.indexItems,
});

// Map Redux actions to component props
const mapDispatchToProps = (dispatch: any): ExamEditorDispatchToProps => {
  return {
    fetchExamById: (payload) => dispatch(fetchExamById(payload)),
    setCurrentExam: (payload) => dispatch(setCurrentExam(payload)),
    updateCurrentExamField: (payload) =>
      dispatch(updateCurrentExamField(payload as any)),
    addSection: (payload) => dispatch(addSection(payload)),
    updateSection: (payload) => dispatch(updateSection(payload)),
    removeSection: (payload) => dispatch(removeSection(payload)),
    addQuestion: (payload) => dispatch(addQuestion(payload)),
    updateQuestion: (payload) => dispatch(updateQuestion(payload)),
    removeQuestion: (payload) => dispatch(removeQuestion(payload)),
    reorderSections: (payload) => dispatch(reorderSections(payload)),
    reorderQuestions: (payload) => dispatch(reorderQuestions(payload)),
    createExam: (payload) => dispatch(createExam(payload)),
    updateExamData: (payload) => dispatch(updateExamData(payload)),
    updateExamStatus: (payload) => dispatch(updateExamStatus(payload)),
    setStateSavePending: (payload) =>
      dispatch(setStateSavePending(payload.stateSavePending)),
    addGroupQuestion: (payload) => dispatch(addGroupQuestion(payload)),
    updateGroupQuestion: (payload) => dispatch(updateGroupQuestion(payload)),
    removeGroupQuestion: (payload) => dispatch(removeGroupQuestion(payload)),
    setOrderedIndexItems: (payload) =>
      dispatch(setOrderedIndexItems(payload.indexItems)),
  };
};

const ExamEditorEnhancedContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(ExamEditorEnhanced);

export default ExamEditorEnhancedContainer;
