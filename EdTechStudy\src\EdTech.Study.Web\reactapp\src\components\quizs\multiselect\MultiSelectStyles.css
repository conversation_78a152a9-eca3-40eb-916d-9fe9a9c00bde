.multi-select-card {
  margin-bottom: 16px;
}

.multi-select-question {
  margin-bottom: 24px;
}

.multi-select-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.option-card {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-card:hover {
  border-color: var(--edtt-color-border-primary);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.option-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.option-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.option-feedback {
  margin-left: 12px;
}

.feedback-icon {
  font-size: 16px;
}

.feedback-icon.correct {
  color: #52c41a;
}

.feedback-icon.incorrect {
  color: #ff4d4f;
}

/* States */
.option-card.selected {
  background-color: var(--edtt-color-selected);
  border-color: var(--edtt-color-border-primary);
}

.option-card.correct {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.option-card.incorrect {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

/* Config mode styles */
.config-option-card {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
}

.config-option-card:hover {
  border-color: var(--edtt-color-border-primary);
}

/* Add styles for MultiSelectResultComponent */
.multiselect-result-list .ant-list-item {
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.multiselect-result-list .ant-list-item.correct {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.multiselect-result-list .ant-list-item.incorrect {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.multiselect-result-explanation {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.multi-select-option-item .ant-input-underlined {
  border-bottom: 1px solid #d9d9d9;
}
