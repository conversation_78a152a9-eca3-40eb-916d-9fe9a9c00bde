﻿using System.Collections.Generic;
using System.Text.Json;
using EdTech.Study.Exams;
using EdTech.Study.Grade;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace EdTech.Study.EntityFrameworkCore
{
    public static class QuestionDraftDbContextModelCreatingExtensions
    {
        public static void ConfigureQuestionDraft(
            this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.ConfigureStudyCategory();

            // QuestionDraft
            builder.Entity<QuestionDraftEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "QuestionDrafts", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
                b.<PERSON><PERSON><PERSON>(x => x.Options)
                   .WithOne(x => x.QuestionDraft)
                   .HasForeignKey(x => x.QuestionDraftId);
            });

            // QuestionOptionDraftEntity
            builder.Entity<QuestionOptionDraftEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "QuestionOptionDrafts", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // Exam
            builder.Entity<ExamEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "Exams", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // ExamSection
            builder.Entity<SectionEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "Sections", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // SectionQuestion
            builder.Entity<SectionQuestionEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "SectionQuestions", StudyDbProperties.DbSchema);
                b.Property(e => e.SortOrders)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                        v => JsonSerializer.Deserialize<List<ItemOrder>>(v, (JsonSerializerOptions)null));
                b.ConfigureByConvention();
            });

            // Question
            builder.Entity<QuestionEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "Questions", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // QuestionOption
            builder.Entity<QuestionOptionEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "QuestionOptions", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // FillInBlankAnswer
            builder.Entity<FillInBlankAnswerEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "FillInBlankAnswers", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // MatchingAnswer
            builder.Entity<MatchingAnswerEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "MatchingAnswers", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // MatchingItem
            builder.Entity<MatchingItemEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "MatchingItems", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // LessonGrade
            builder.Entity<LessonGrade>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "LessonGrades", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // GroupQuestion
            builder.Entity<GroupQuestionEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "GroupQuestions", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // GroupQuestion
            builder.Entity<SectionGroupQuestionEntity>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "SectionGroupQuestions", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });
        }
    }
}