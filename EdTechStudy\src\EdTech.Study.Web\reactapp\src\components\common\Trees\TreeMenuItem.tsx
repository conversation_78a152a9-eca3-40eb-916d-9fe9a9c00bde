import { RightOutlined, DownOutlined, BookFilled } from '@ant-design/icons';
import React, { useState } from 'react';
import { IBookMarkItem } from '../../../interfaces/commonInterfaces';
import { Collapse } from 'antd';
import './treeMenuItem.css';

// Enhanced type that includes children array
type TreeItemType = IBookMarkItem & { children: TreeItemType[] };

// Component to recursively render menu items
const TreeMenuItem = ({
  item,
  level = 0,
  onClick,
  autoExpand = false,
}: {
  item: TreeItemType;
  level?: number;
  autoExpand?: boolean;
  onClick?: (id: string) => any;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const hasChildren = item.children && item.children.length > 0;

  // Handler for toggling expansion
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Base menu item that will be displayed at all levels
  const menuItem = (
    <a
      href={'#' + item.id}
      className={
        'tree-menu-link tailwind-btn tailwind-block tailwind-text-nowrap tailwind-text-ellipsis tailwind-overflow-hidden ' +
        (level <= 1 ? 'tailwind-font-medium ' : '')
      }
      onClick={(e) => {
        if (hasChildren && level >= 1) {
          // If this is a parent item with children and level >= 1, toggle expansion
          toggleExpand(e);
        } else {
          // Otherwise, proceed with the click handler
          e.preventDefault();
          onClick && onClick(item.id);
        }
      }}
    >
      <div className="tailwind-flex tailwind-gap-2">
        {item.marked && (
          <span className="tailwind-text-primary">
            <BookFilled />
          </span>
        )}
        {!item.marked && (
          <span className="tailwind-text-primary">
            {item.icon ||
              (hasChildren ? (
                isExpanded ? (
                  <DownOutlined />
                ) : (
                  <RightOutlined />
                )
              ) : (
                <RightOutlined />
              ))}
          </span>
        )}
        <span>{item.displayName}</span>
      </div>
    </a>
  );

  // If this is a root level item or level 0, render normally
  if (level < 1 || !hasChildren) {
    return (
      <div className="tailwind-py-1">
        {menuItem}
        {hasChildren && (
          <div className="tailwind-pl-4">
            {item.children.map((child) => (
              <TreeMenuItem
                key={child.id}
                item={child}
                level={level + 1}
                onClick={onClick}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // For level >= 1 with children, use the Collapse component
  const collapseItems = [
    {
      key: '1',
      label: menuItem,
      children: (
        <div className="tailwind-pl-4">
          {item.children.map((child) => (
            <TreeMenuItem
              key={child.id}
              item={child}
              level={level + 1}
              onClick={onClick}
              autoExpand={autoExpand}
            />
          ))}
        </div>
      ),
    },
  ];
  return (
    <div className="tailwind-py-1">
      <Collapse
        defaultActiveKey={autoExpand ? ['1'] : []}
        items={collapseItems}
        bordered={false}
        expandIcon={() => null} // We'll handle our own expand icon
        className="tailwind-bg-transparent"
        activeKey={isExpanded ? ['1'] : []}
      ></Collapse>
    </div>
  );
};

export default TreeMenuItem;
