import {
  MatchingQuestion,
  MatchingQuestionAnswer,
  MatchingItem,
} from '../../../interfaces/quizs/mapping.interface';
import { MatchingItemType } from '../../../interfaces/exams/examEnums';
import {
  BaseAnswer,
  BaseQuestion,
} from '../../../interfaces/quizs/questionBase';

/**
 * Check if the user's answer is correct for matching question
 * @param question The matching question
 * @param userAnswers User's selected answers
 * @returns true if all answers are correct, false otherwise, null if cannot determine
 */
export const matchingCheckCorrectAnswer = (
  question: BaseQuestion,
  userSelect?: BaseAnswer | BaseAnswer[]
): boolean | null => {
  // Check if we have necessary data
  if (!userSelect || !Array.isArray(userSelect)) return null;
  const matchingQuestion = question as MatchingQuestion;
  const userAnswers = userSelect as MatchingQuestionAnswer[];
  if (
    !matchingQuestion.matchingItems ||
    !matchingQuestion.matchingAnswers ||
    matchingQuestion.matchingItems.length === 0 ||
    matchingQuestion.matchingAnswers.length === 0
  ) {
    return null;
  }

  // Get premise items (left column)
  const premiseItems = matchingQuestion.matchingItems.filter(
    (item) => item.type === MatchingItemType.Premise
  );

  // Get response items (right column)
  const responseItems = matchingQuestion.matchingItems.filter(
    (item) => item.type === MatchingItemType.Response
  );

  if (premiseItems.length === 0 || responseItems.length === 0) {
    return null;
  }

  // Get correct answers from question definition
  const correctAnswers = matchingQuestion.matchingAnswers;

  // Create a set of correct connections for quick lookup
  const correctConnectionsSet = new Set<string>();
  correctAnswers.forEach((answer) => {
    correctConnectionsSet.add(`${answer.premiseId}-${answer.responseId}`);
  });

  // Create a set of user connections
  const userConnectionsSet = new Set<string>();
  userAnswers.forEach((answer) => {
    userConnectionsSet.add(`${answer.premiseId}-${answer.responseId}`);
  });

  // Check if all user connections are correct
  const allUserConnectionsCorrect = Array.from(userConnectionsSet).every(
    (connection) => correctConnectionsSet.has(connection)
  );

  // Check if all correct connections are present in user answers
  const allCorrectConnectionsPresent = Array.from(correctConnectionsSet).every(
    (connection) => userConnectionsSet.has(connection)
  );

  // Return true only if:
  // 1. All user connections are correct (no wrong connections)
  // 2. All required correct connections are present (complete answer)
  return allUserConnectionsCorrect && allCorrectConnectionsPresent;
};

/**
 * Get completion percentage for matching question
 * @param question The matching question
 * @param userAnswers User's selected answers
 * @returns Completion percentage (0-100)
 */
export const getMatchingCompletionPercentage = (
  question: MatchingQuestion,
  userAnswers: MatchingQuestionAnswer[]
): number => {
  if (!question.matchingAnswers || question.matchingAnswers.length === 0) {
    return 0;
  }

  const totalRequired = question.matchingAnswers.length;
  const userCompleted = userAnswers ? userAnswers.length : 0;

  return Math.min(100, Math.round((userCompleted / totalRequired) * 100));
};

/**
 * Validate matching question configuration
 * @param question The matching question to validate
 * @returns Object with validation result and error messages
 */
export const validateMatchingQuestion = (
  question: MatchingQuestion
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check basic question properties
  if (!question.title || question.title.trim() === '') {
    errors.push('Tiêu đề câu hỏi không được để trống');
  }

  if (!question.content || question.content.trim() === '') {
    errors.push('Nội dung câu hỏi không được để trống');
  }

  // Check matching items
  if (!question.matchingItems || question.matchingItems.length === 0) {
    errors.push('Câu hỏi phải có ít nhất một mục để ghép cặp');
  } else {
    const premiseItems = question.matchingItems.filter(
      (item) => item.type === MatchingItemType.Premise
    );
    const responseItems = question.matchingItems.filter(
      (item) => item.type === MatchingItemType.Response
    );

    if (premiseItems.length === 0) {
      errors.push('Phải có ít nhất một mục trong cột 1 (Premise)');
    }

    if (responseItems.length === 0) {
      errors.push('Phải có ít nhất một mục trong cột 2 (Response)');
    }

    // Check if all items have content
    const emptyItems = question.matchingItems.filter(
      (item) => !item.content || item.content.trim() === ''
    );
    if (emptyItems.length > 0) {
      errors.push('Tất cả các mục phải có nội dung');
    }
  }

  // Check matching answers (correct connections)
  if (!question.matchingAnswers || question.matchingAnswers.length === 0) {
    errors.push('Phải định nghĩa ít nhất một kết nối đúng');
  } else {
    // Validate that all premise and response IDs in answers exist in items
    const premiseIds = new Set(
      question.matchingItems
        ?.filter((item) => item.type === MatchingItemType.Premise)
        .map((item) => item.clientId) || []
    );
    const responseIds = new Set(
      question.matchingItems
        ?.filter((item) => item.type === MatchingItemType.Response)
        .map((item) => item.clientId) || []
    );

    const invalidAnswers = question.matchingAnswers.filter(
      (answer) =>
        !premiseIds.has(answer.premiseId) || !responseIds.has(answer.responseId)
    );

    if (invalidAnswers.length > 0) {
      errors.push('Một số kết nối đúng tham chiếu đến các mục không tồn tại');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Get partial score for matching question
 * @param question The matching question
 * @param userAnswers User's selected answers
 * @returns Partial score between 0 and 1
 */
export const getMatchingPartialScore = (
  question: MatchingQuestion,
  userAnswers: MatchingQuestionAnswer[]
): number => {
  if (!userAnswers || userAnswers.length === 0) return 0;

  if (!question.matchingAnswers || question.matchingAnswers.length === 0) {
    return 0;
  }

  const correctAnswers = question.matchingAnswers;
  const totalCorrect = correctAnswers.length;

  // Count how many user answers are correct
  let correctCount = 0;
  userAnswers.forEach((userAnswer) => {
    const isCorrect = correctAnswers.some(
      (correctAnswer) =>
        correctAnswer.premiseId === userAnswer.premiseId &&
        correctAnswer.responseId === userAnswer.responseId
    );
    if (isCorrect) {
      correctCount++;
    }
  });

  return correctCount / totalCorrect;
};

/**
 * Get detailed feedback for matching question
 * @param question The matching question
 * @param userAnswers User's selected answers
 * @returns Detailed feedback object
 */
export const getMatchingFeedback = (
  question: MatchingQuestion,
  userAnswers: MatchingQuestionAnswer[]
): {
  isCorrect: boolean | null;
  correctConnections: number;
  totalConnections: number;
  incorrectConnections: string[];
  missingConnections: string[];
} => {
  const result = {
    isCorrect: null as boolean | null,
    correctConnections: 0,
    totalConnections: 0,
    incorrectConnections: [] as string[],
    missingConnections: [] as string[],
  };

  if (!question.matchingAnswers || question.matchingAnswers.length === 0) {
    return result;
  }

  const correctAnswers = question.matchingAnswers;
  result.totalConnections = correctAnswers.length;

  // Create maps for easier lookup
  const correctConnectionsMap = new Map<string, string>();
  correctAnswers.forEach((answer) => {
    correctConnectionsMap.set(answer.premiseId, answer.responseId);
  });

  const userConnectionsMap = new Map<string, string>();
  (userAnswers || []).forEach((answer) => {
    userConnectionsMap.set(answer.premiseId, answer.responseId);
  });

  // Check each correct connection
  correctAnswers.forEach((correctAnswer) => {
    const userResponse = userConnectionsMap.get(correctAnswer.premiseId);

    if (userResponse === correctAnswer.responseId) {
      result.correctConnections++;
    } else {
      // Find the premise and response items for better feedback
      const premiseItem = question.matchingItems?.find(
        (item) => item.clientId === correctAnswer.premiseId
      );
      const responseItem = question.matchingItems?.find(
        (item) => item.clientId === correctAnswer.responseId
      );

      if (premiseItem && responseItem) {
        result.missingConnections.push(
          `${premiseItem.content} → ${responseItem.content}`
        );
      }
    }
  });

  // Check for incorrect connections
  userConnectionsMap.forEach((responseId, premiseId) => {
    const correctResponseId = correctConnectionsMap.get(premiseId);

    if (correctResponseId !== responseId) {
      const premiseItem = question.matchingItems?.find(
        (item) => item.clientId === premiseId
      );
      const responseItem = question.matchingItems?.find(
        (item) => item.clientId === responseId
      );

      if (premiseItem && responseItem) {
        result.incorrectConnections.push(
          `${premiseItem.content} → ${responseItem.content}`
        );
      }
    }
  });

  // Determine if answer is correct
  result.isCorrect = matchingCheckCorrectAnswer(question, userAnswers || []);

  return result;
};

/**
 * Get premise items from the matching items array
 * @param matchingItems Array of matching items
 * @returns Array of premise items
 */
export const getPremiseItems = (
  matchingItems: MatchingItem[]
): MatchingItem[] => {
  return matchingItems.filter((item) => item.type === MatchingItemType.Premise);
};

/**
 * Get response items from the matching items array
 * @param matchingItems Array of matching items
 * @returns Array of response items
 */
export const getResponseItems = (
  matchingItems: MatchingItem[]
): MatchingItem[] => {
  return matchingItems.filter(
    (item) => item.type === MatchingItemType.Response
  );
};

/**
 * Find the matching response items for a premise item
 * @param premiseItem The premise item
 * @param responseItems Array of response items
 * @param matchingAnswers Array of matching answers
 * @returns Array of matching response items
 */
export const findMatchingResponseItems = (
  premiseItem: MatchingItem,
  responseItems: MatchingItem[],
  matchingAnswers: MatchingQuestionAnswer[]
): MatchingItem[] => {
  // Find response IDs from matching answers
  const responseIds = matchingAnswers
    .filter((answer) => answer.premiseId === premiseItem.clientId)
    .map((answer) => answer.responseId);

  // Return response items that match these IDs
  return responseItems.filter((item) => responseIds.includes(item.clientId));
};

/**
 * Find a matching item by its client ID
 * @param items Array of matching items
 * @param clientId The client ID to search for
 * @returns The matching item or undefined
 */
export const findItemById = (
  items: MatchingItem[],
  clientId: string
): MatchingItem | undefined => {
  return items.find((item) => item.clientId === clientId);
};

/**
 * Convert matching items to legacy left/right format
 * @param matchingItems Array of matching items
 * @returns Object containing leftItems and rightItems arrays
 */
export const convertToLegacyFormat = (
  matchingItems: MatchingItem[],
  matchingAnswers: MatchingQuestionAnswer[]
): {
  leftItems: any[];
  rightItems: any[];
} => {
  const premiseItems = getPremiseItems(matchingItems);
  const responseItems = getResponseItems(matchingItems);

  const leftItems = premiseItems.map((item) => {
    // Find response IDs that match this premise
    const matchIds = matchingAnswers
      .filter((answer) => answer.premiseId === item.clientId)
      .map((answer) => answer.responseId);

    return {
      ...item,
      matchIds,
    };
  });

  const rightItems = responseItems.map((item) => {
    // Find premise IDs that match this response
    const matchIds = matchingAnswers
      .filter((answer) => answer.responseId === item.clientId)
      .map((answer) => answer.premiseId);

    return {
      ...item,
      matchIds,
    };
  });

  return {
    leftItems,
    rightItems,
  };
};

/**
 * Convert legacy format to matching items and answers
 * @param leftItems Legacy left items
 * @param rightItems Legacy right items
 * @returns Object containing matchingItems and matchingAnswers
 */
export const convertFromLegacyFormat = (
  leftItems: any[],
  rightItems: any[]
): {
  matchingItems: MatchingItem[];
  matchingAnswers: MatchingQuestionAnswer[];
} => {
  const matchingItems: MatchingItem[] = [
    ...leftItems.map((item) => ({
      ...item,
      type: MatchingItemType.Premise,
    })),
    ...rightItems.map((item) => ({
      ...item,
      type: MatchingItemType.Response,
    })),
  ];

  const matchingAnswers: MatchingQuestionAnswer[] = [];

  // Create matching answers from left items' matchIds
  leftItems.forEach((leftItem) => {
    if (leftItem.matchIds && leftItem.matchIds.length > 0) {
      leftItem.matchIds.forEach((rightId: string) => {
        matchingAnswers.push({
          id: undefined,
          clientId: `${leftItem.clientId}_${rightId}`,
          premiseId: leftItem.clientId,
          responseId: rightId,
          content: '',
          contentFormat: '',
          order: matchingAnswers.length,
          isCorrect: true,
          score: 1,
        });
      });
    }
  });

  return {
    matchingItems,
    matchingAnswers,
  };
};
