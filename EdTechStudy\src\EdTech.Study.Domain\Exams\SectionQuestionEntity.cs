﻿using EdTech.Study.Questions;
using System;
using System.Collections.Generic;
using System.Text;
using Volo.Abp.Domain.Entities;
using System.Text.Json;

namespace EdTech.Study.Exams
{
    /// <summary>
    /// Bảng trung gian giữa phần thi và câu hỏi.
    /// </summary>
    public class SectionQuestionEntity : Entity<Guid>
    {
        public SectionQuestionEntity()
        {

        }

        public SectionQuestionEntity(Guid key) : base(key)
        {

        }

        /// <summary>
        /// Id của phần thi.
        /// </summary>
        public Guid SectionId { get; set; }

        /// <summary>
        ///  Id Câu hỏi template
        /// Sử dụng để backup khi bật tắt đồng bộ
        /// </summary>
        public Guid? LastSyncQuestionId { get; set; }

        /// <summary>
        /// Id của câu hỏi.
        /// </summary>
        public Guid QuestionId { get; set; }

        /// <summary>
        /// Navigation đến phần thi.
        /// </summary>
        public SectionEntity Section { get; set; }

        /// <summary>
        /// Navigation đến câu hỏi.
        /// </summary>
        public QuestionEntity Question { get; set; }

        /// <summary>
        /// Thứ tự câu hỏi trong phần thi (tùy chọn thêm).
        /// </summary>
        public int Order { get; set; }
        /// <summary>
        /// Cho biết câu hỏi có đang đồng bộ với mẫu hay không.
        /// Nếu là true, câu hỏi sẽ được đồng bộ với mẫu câu hỏi trong ngân hàng đề.
        /// và không thể chỉnh sửa nội dung câu hỏi này.
        /// </summary>
        public bool SyncQuestion { get; set; }

        /// <summary>
        /// Điểm số tối đa của câu hỏi.
        /// </summary>
        public float? Score { get; set; }

        /// <summary>
        /// Lưu thứ tự của đáp án
        /// </summary>
        public List<ItemOrder>? SortOrders { get; set; }
    }
}
